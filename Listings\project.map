Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]

==============================================================================

Section Cross References

    main.o(i.TIM2_IRQHandler) refers to stm32f10x_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    main.o(i.TIM2_IRQHandler) refers to stm32f10x_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    main.o(i.TIM2_IRQHandler) refers to key.o(i.key_Loop) for key_Loop
    main.o(i.TIM2_IRQHandler) refers to my_pid.o(i.Target_practice) for Target_practice
    main.o(i.TIM2_IRQHandler) refers to menu.o(.data) for Mode
    main.o(i.main) refers to ha_oled.o(i.OLED_Init) for OLED_Init
    main.o(i.main) refers to key.o(i.KEY_Init) for KEY_Init
    main.o(i.main) refers to jg.o(i.JG_Init) for JG_Init
    main.o(i.main) refers to fifo.o(i.Init_Queue) for Init_Queue
    main.o(i.main) refers to fifo.o(i.Init_Queue_3) for Init_Queue_3
    main.o(i.main) refers to serial.o(i.DoubleBuffer_Init) for DoubleBuffer_Init
    main.o(i.main) refers to serial.o(i.Serial_Init) for Serial_Init
    main.o(i.main) refers to stepping_motor.o(i.My_motor_Init) for My_motor_Init
    main.o(i.main) refers to timer.o(i.Timer_Init) for Timer_Init
    main.o(i.main) refers to key.o(i.Key_GetNum) for Key_GetNum
    main.o(i.main) refers to ha_oled.o(i.OLED_Printf) for OLED_Printf
    main.o(i.main) refers to ha_oled.o(i.OLED_Clear) for OLED_Clear
    main.o(i.main) refers to ha_oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.main) refers to ha_oled.o(i.OLED_Update) for OLED_Update
    main.o(i.main) refers to jg.o(i.JG_Ton) for JG_Ton
    main.o(i.main) refers to stepping_motor.o(i.Motor_Enable) for Motor_Enable
    main.o(i.main) refers to delay.o(i.Delay_ms) for Delay_ms
    main.o(i.main) refers to stepping_motor.o(i.Set_zero_position) for Set_zero_position
    main.o(i.main) refers to jg.o(i.JG_Toff) for JG_Toff
    main.o(i.main) refers to stepping_motor.o(i.Return_zero) for Return_zero
    main.o(i.main) refers to serial.o(i.Serial_GetRxflag_2) for Serial_GetRxflag_2
    main.o(i.main) refers to serial.o(i.Serial_GetRxflag_3) for Serial_GetRxflag_3
    main.o(i.main) refers to my_pid.o(i.app_pid_init) for app_pid_init
    main.o(i.main) refers to stepping_motor.o(i.V_Control) for V_Control
    main.o(i.main) refers to menu.o(.data) for Mode
    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(STACK) for __initial_sp
    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(.text) for Reset_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f10x_md.o(RESET) refers to main.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to serial.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to serial.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to serial.o(i.USART3_IRQHandler) for USART3_IRQHandler
    startup_stm32f10x_md.o(.text) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    startup_stm32f10x_md.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    system_stm32f10x.o(i.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data) for .data
    system_stm32f10x.o(i.SystemInit) refers to system_stm32f10x.o(i.SetSysClockTo72) for SetSysClockTo72
    stm32f10x_adc.o(i.ADC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_bkp.o(i.BKP_DeInit) refers to stm32f10x_rcc.o(i.RCC_BackupResetCmd) for RCC_BackupResetCmd
    stm32f10x_can.o(i.CAN_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_can.o(i.CAN_GetITStatus) refers to stm32f10x_can.o(i.CheckITStatus) for CheckITStatus
    stm32f10x_cec.o(i.CEC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_dac.o(i.DAC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_flash.o(i.FLASH_EnableWriteProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) for FLASH_WaitForLastBank1Operation
    stm32f10x_flash.o(i.FLASH_EraseAllPages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus) for FLASH_GetReadOutProtectionStatus
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ErasePage) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramHalfWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramOptionByteData) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ReadOutProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_UserOptionByteConfig) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_pwr.o(i.PWR_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.data) for .data
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_spi.o(i.I2S_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(i.USART_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_wwdg.o(i.WWDG_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    delay.o(i.Delay_ms) refers to delay.o(i.Delay_us) for Delay_us
    delay.o(i.Delay_s) refers to delay.o(i.Delay_ms) for Delay_ms
    timer.o(i.Timer_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    timer.o(i.Timer_Init) refers to stm32f10x_tim.o(i.TIM_InternalClockConfig) for TIM_InternalClockConfig
    timer.o(i.Timer_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    timer.o(i.Timer_Init) refers to stm32f10x_tim.o(i.TIM_ClearFlag) for TIM_ClearFlag
    timer.o(i.Timer_Init) refers to stm32f10x_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    timer.o(i.Timer_Init) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    timer.o(i.Timer_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    timer.o(i.Timer_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    fifo.o(i.Init_Queue) refers to fifo.o(.bss) for .bss
    fifo.o(i.Init_Queue_3) refers to fifo.o(.bss) for .bss
    fifo.o(i.fifo_In) refers to fifo.o(.bss) for .bss
    fifo.o(i.fifo_In_3) refers to fifo.o(.bss) for .bss
    fifo.o(i.fifo_Out) refers to fifo.o(.bss) for .bss
    fifo.o(i.fifo_Out_3) refers to fifo.o(.bss) for .bss
    fifo.o(i.fifo_isEmpty) refers to fifo.o(.bss) for .bss
    fifo.o(i.fifo_isEmpty_3) refers to fifo.o(.bss) for .bss
    fifo.o(i.fifo_queueLength) refers to fifo.o(.bss) for .bss
    fifo.o(i.fifo_queueLength_3) refers to fifo.o(.bss) for .bss
    ha_oled.o(i.Ha_IIC_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    ha_oled.o(i.Ha_IIC_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    ha_oled.o(i.Ha_IIC_Init) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    ha_oled.o(i.Ha_IIC_R_SDA) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    ha_oled.o(i.Ha_IIC_ReceiveAck) refers to ha_oled.o(i.Ha_IIC_W_SDA) for Ha_IIC_W_SDA
    ha_oled.o(i.Ha_IIC_ReceiveAck) refers to ha_oled.o(i.Ha_IIC_W_SCL) for Ha_IIC_W_SCL
    ha_oled.o(i.Ha_IIC_ReceiveAck) refers to ha_oled.o(i.Ha_IIC_R_SDA) for Ha_IIC_R_SDA
    ha_oled.o(i.Ha_IIC_SendAck) refers to ha_oled.o(i.Ha_IIC_W_SDA) for Ha_IIC_W_SDA
    ha_oled.o(i.Ha_IIC_SendAck) refers to ha_oled.o(i.Ha_IIC_W_SCL) for Ha_IIC_W_SCL
    ha_oled.o(i.Ha_IIC_Start) refers to ha_oled.o(i.Ha_IIC_W_SDA) for Ha_IIC_W_SDA
    ha_oled.o(i.Ha_IIC_Start) refers to ha_oled.o(i.Ha_IIC_W_SCL) for Ha_IIC_W_SCL
    ha_oled.o(i.Ha_IIC_Stop) refers to ha_oled.o(i.Ha_IIC_W_SDA) for Ha_IIC_W_SDA
    ha_oled.o(i.Ha_IIC_Stop) refers to ha_oled.o(i.Ha_IIC_W_SCL) for Ha_IIC_W_SCL
    ha_oled.o(i.Ha_IIC_W_SCL) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    ha_oled.o(i.Ha_IIC_W_SDA) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    ha_oled.o(i.Ha_ReceiveByte) refers to ha_oled.o(i.Ha_IIC_W_SDA) for Ha_IIC_W_SDA
    ha_oled.o(i.Ha_ReceiveByte) refers to ha_oled.o(i.Ha_IIC_W_SCL) for Ha_IIC_W_SCL
    ha_oled.o(i.Ha_ReceiveByte) refers to ha_oled.o(i.Ha_IIC_R_SDA) for Ha_IIC_R_SDA
    ha_oled.o(i.Ha_SendByte) refers to ha_oled.o(i.Ha_IIC_W_SDA) for Ha_IIC_W_SDA
    ha_oled.o(i.Ha_SendByte) refers to ha_oled.o(i.Ha_IIC_W_SCL) for Ha_IIC_W_SCL
    ha_oled.o(i.OLED_Clear) refers to ha_oled.o(.bss) for .bss
    ha_oled.o(i.OLED_ClearArea) refers to ha_oled.o(.bss) for .bss
    ha_oled.o(i.OLED_DrawArc) refers to ha_oled.o(i.OLED_IsInAngle) for OLED_IsInAngle
    ha_oled.o(i.OLED_DrawArc) refers to ha_oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    ha_oled.o(i.OLED_DrawCircle) refers to ha_oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    ha_oled.o(i.OLED_DrawEllipse) refers to dflti.o(.text) for __aeabi_i2d
    ha_oled.o(i.OLED_DrawEllipse) refers to dadd.o(.text) for __aeabi_dadd
    ha_oled.o(i.OLED_DrawEllipse) refers to dmul.o(.text) for __aeabi_dmul
    ha_oled.o(i.OLED_DrawEllipse) refers to d2f.o(.text) for __aeabi_d2f
    ha_oled.o(i.OLED_DrawEllipse) refers to ha_oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    ha_oled.o(i.OLED_DrawEllipse) refers to cdcmple.o(.text) for __aeabi_cdcmple
    ha_oled.o(i.OLED_DrawEllipse) refers to fflti.o(.text) for __aeabi_i2f
    ha_oled.o(i.OLED_DrawEllipse) refers to fadd.o(.text) for __aeabi_fadd
    ha_oled.o(i.OLED_DrawLine) refers to ha_oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    ha_oled.o(i.OLED_DrawPoint) refers to ha_oled.o(.bss) for .bss
    ha_oled.o(i.OLED_DrawRectangle) refers to ha_oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    ha_oled.o(i.OLED_DrawTriangle) refers to ha_oled.o(i.OLED_DrawLine) for OLED_DrawLine
    ha_oled.o(i.OLED_DrawTriangle) refers to ha_oled.o(i.OLED_pnpoly) for OLED_pnpoly
    ha_oled.o(i.OLED_DrawTriangle) refers to ha_oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    ha_oled.o(i.OLED_GetPoint) refers to ha_oled.o(.bss) for .bss
    ha_oled.o(i.OLED_Init) refers to ha_oled.o(i.Ha_IIC_Init) for Ha_IIC_Init
    ha_oled.o(i.OLED_Init) refers to ha_oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    ha_oled.o(i.OLED_Init) refers to ha_oled.o(i.OLED_Clear) for OLED_Clear
    ha_oled.o(i.OLED_Init) refers to ha_oled.o(i.OLED_Update) for OLED_Update
    ha_oled.o(i.OLED_IsInAngle) refers to dflti.o(.text) for __aeabi_i2d
    ha_oled.o(i.OLED_IsInAngle) refers to atan2.o(i.atan2) for atan2
    ha_oled.o(i.OLED_IsInAngle) refers to ddiv.o(.text) for __aeabi_ddiv
    ha_oled.o(i.OLED_IsInAngle) refers to dmul.o(.text) for __aeabi_dmul
    ha_oled.o(i.OLED_IsInAngle) refers to dfixi.o(.text) for __aeabi_d2iz
    ha_oled.o(i.OLED_Printf) refers to printfa.o(i.__0vsprintf) for vsprintf
    ha_oled.o(i.OLED_Printf) refers to ha_oled.o(i.OLED_ShowString) for OLED_ShowString
    ha_oled.o(i.OLED_Reverse) refers to ha_oled.o(.bss) for .bss
    ha_oled.o(i.OLED_ReverseArea) refers to ha_oled.o(.bss) for .bss
    ha_oled.o(i.OLED_SetCursor) refers to ha_oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    ha_oled.o(i.OLED_ShowBinNum) refers to ha_oled.o(i.OLED_Pow) for OLED_Pow
    ha_oled.o(i.OLED_ShowBinNum) refers to ha_oled.o(i.OLED_ShowChar) for OLED_ShowChar
    ha_oled.o(i.OLED_ShowChar) refers to ha_oled.o(i.OLED_ShowImage) for OLED_ShowImage
    ha_oled.o(i.OLED_ShowChar) refers to ha_oleddata.o(.constdata) for OLED_F8x16
    ha_oled.o(i.OLED_ShowChar) refers to ha_oleddata.o(.constdata) for OLED_F6x8
    ha_oled.o(i.OLED_ShowChinese) refers to strcmp.o(.text) for strcmp
    ha_oled.o(i.OLED_ShowChinese) refers to ha_oled.o(i.OLED_ShowImage) for OLED_ShowImage
    ha_oled.o(i.OLED_ShowChinese) refers to ha_oleddata.o(.constdata) for OLED_CF16x16
    ha_oled.o(i.OLED_ShowFloatNum) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    ha_oled.o(i.OLED_ShowFloatNum) refers to ha_oled.o(i.OLED_ShowChar) for OLED_ShowChar
    ha_oled.o(i.OLED_ShowFloatNum) refers to dfixui.o(.text) for __aeabi_d2uiz
    ha_oled.o(i.OLED_ShowFloatNum) refers to dfltui.o(.text) for __aeabi_ui2d
    ha_oled.o(i.OLED_ShowFloatNum) refers to dadd.o(.text) for __aeabi_drsub
    ha_oled.o(i.OLED_ShowFloatNum) refers to ha_oled.o(i.OLED_Pow) for OLED_Pow
    ha_oled.o(i.OLED_ShowFloatNum) refers to dmul.o(.text) for __aeabi_dmul
    ha_oled.o(i.OLED_ShowFloatNum) refers to round.o(i.round) for round
    ha_oled.o(i.OLED_ShowFloatNum) refers to ha_oled.o(i.OLED_ShowNum) for OLED_ShowNum
    ha_oled.o(i.OLED_ShowHexNum) refers to ha_oled.o(i.OLED_Pow) for OLED_Pow
    ha_oled.o(i.OLED_ShowHexNum) refers to ha_oled.o(i.OLED_ShowChar) for OLED_ShowChar
    ha_oled.o(i.OLED_ShowImage) refers to ha_oled.o(i.OLED_ClearArea) for OLED_ClearArea
    ha_oled.o(i.OLED_ShowImage) refers to ha_oled.o(.bss) for .bss
    ha_oled.o(i.OLED_ShowNum) refers to ha_oled.o(i.OLED_Pow) for OLED_Pow
    ha_oled.o(i.OLED_ShowNum) refers to ha_oled.o(i.OLED_ShowChar) for OLED_ShowChar
    ha_oled.o(i.OLED_ShowSignedNum) refers to ha_oled.o(i.OLED_ShowChar) for OLED_ShowChar
    ha_oled.o(i.OLED_ShowSignedNum) refers to ha_oled.o(i.OLED_Pow) for OLED_Pow
    ha_oled.o(i.OLED_ShowString) refers to ha_oled.o(i.OLED_ShowChar) for OLED_ShowChar
    ha_oled.o(i.OLED_ShowString) refers to strcmp.o(.text) for strcmp
    ha_oled.o(i.OLED_ShowString) refers to ha_oled.o(i.OLED_ShowImage) for OLED_ShowImage
    ha_oled.o(i.OLED_ShowString) refers to ha_oleddata.o(.constdata) for OLED_CF16x16
    ha_oled.o(i.OLED_Update) refers to ha_oled.o(i.OLED_SetCursor) for OLED_SetCursor
    ha_oled.o(i.OLED_Update) refers to ha_oled.o(i.OLED_WriteData) for OLED_WriteData
    ha_oled.o(i.OLED_Update) refers to ha_oled.o(.bss) for .bss
    ha_oled.o(i.OLED_UpdateArea) refers to ha_oled.o(i.OLED_SetCursor) for OLED_SetCursor
    ha_oled.o(i.OLED_UpdateArea) refers to ha_oled.o(i.OLED_WriteData) for OLED_WriteData
    ha_oled.o(i.OLED_UpdateArea) refers to ha_oled.o(.bss) for .bss
    ha_oled.o(i.OLED_WriteCommand) refers to ha_oled.o(i.Ha_IIC_Start) for Ha_IIC_Start
    ha_oled.o(i.OLED_WriteCommand) refers to ha_oled.o(i.Ha_SendByte) for Ha_SendByte
    ha_oled.o(i.OLED_WriteCommand) refers to ha_oled.o(i.Ha_IIC_ReceiveAck) for Ha_IIC_ReceiveAck
    ha_oled.o(i.OLED_WriteCommand) refers to ha_oled.o(i.Ha_IIC_Stop) for Ha_IIC_Stop
    ha_oled.o(i.OLED_WriteData) refers to ha_oled.o(i.Ha_IIC_Start) for Ha_IIC_Start
    ha_oled.o(i.OLED_WriteData) refers to ha_oled.o(i.Ha_SendByte) for Ha_SendByte
    ha_oled.o(i.OLED_WriteData) refers to ha_oled.o(i.Ha_IIC_ReceiveAck) for Ha_IIC_ReceiveAck
    ha_oled.o(i.OLED_WriteData) refers to ha_oled.o(i.Ha_IIC_Stop) for Ha_IIC_Stop
    key.o(i.KEY_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    key.o(i.KEY_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    key.o(i.Key_GetNum) refers to key.o(.data) for .data
    key.o(i.Key_Getstate) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    key.o(i.key_Loop) refers to key.o(i.Key_Getstate) for Key_Getstate
    key.o(i.key_Loop) refers to key.o(.data) for .data
    stepping_motor.o(i.Change_tozero_parameter) refers to memseta.o(.text) for __aeabi_memclr4
    stepping_motor.o(i.Change_tozero_parameter) refers to serial.o(i.Serial_SendArray) for Serial_SendArray
    stepping_motor.o(i.Contact_LockedRotor_Protect) refers to serial.o(i.Serial_SendArray) for Serial_SendArray
    stepping_motor.o(i.Interrupt_return_zero) refers to serial.o(i.Serial_SendArray) for Serial_SendArray
    stepping_motor.o(i.Many_Motor) refers to serial.o(i.Serial_SendArray) for Serial_SendArray
    stepping_motor.o(i.Modify_Ctrl_Mode) refers to serial.o(i.Serial_SendArray) for Serial_SendArray
    stepping_motor.o(i.Motor_Enable) refers to serial.o(i.Serial_SendArray) for Serial_SendArray
    stepping_motor.o(i.Motor_Set_Speed) refers to stepping_motor.o(i.V_Control) for V_Control
    stepping_motor.o(i.My_motor_Init) refers to stepping_motor.o(i.Motor_Enable) for Motor_Enable
    stepping_motor.o(i.Read_Params) refers to serial.o(i.Serial_SendArray) for Serial_SendArray
    stepping_motor.o(i.Reset_To_Zero) refers to serial.o(i.Serial_SendArray) for Serial_SendArray
    stepping_motor.o(i.Return_zero) refers to serial.o(i.Serial_SendArray) for Serial_SendArray
    stepping_motor.o(i.S_Control) refers to serial.o(i.Serial_SendArray) for Serial_SendArray
    stepping_motor.o(i.Set_zero_position) refers to serial.o(i.Serial_SendArray) for Serial_SendArray
    stepping_motor.o(i.Step_Motor_Set_Pwm) refers to stepping_motor.o(i.S_Control) for S_Control
    stepping_motor.o(i.Step_Motor_Set_Speed_my) refers to fmul.o(.text) for __aeabi_fmul
    stepping_motor.o(i.Step_Motor_Set_Speed_my) refers to fadd.o(.text) for __aeabi_fadd
    stepping_motor.o(i.Step_Motor_Set_Speed_my) refers to ffixui.o(.text) for __aeabi_f2uiz
    stepping_motor.o(i.Step_Motor_Set_Speed_my) refers to stepping_motor.o(i.V_Control) for V_Control
    stepping_motor.o(i.Stop_Now) refers to serial.o(i.Serial_SendArray) for Serial_SendArray
    stepping_motor.o(i.V_Control) refers to serial.o(i.Serial_SendArray) for Serial_SendArray
    serial.o(i.DoubleBuffer_Init) refers to serial.o(.bss) for .bss
    serial.o(i.Get_light_data) refers to serial.o(.bss) for .bss
    serial.o(i.Get_light_data) refers to serial.o(.data) for .data
    serial.o(i.Get_packet_type) refers to serial.o(.bss) for .bss
    serial.o(i.Serial_GetRxflag_1) refers to serial.o(.data) for .data
    serial.o(i.Serial_GetRxflag_2) refers to serial.o(.data) for .data
    serial.o(i.Serial_GetRxflag_3) refers to serial.o(.data) for .data
    serial.o(i.Serial_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    serial.o(i.Serial_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    serial.o(i.Serial_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    serial.o(i.Serial_Init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    serial.o(i.Serial_Init) refers to stm32f10x_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    serial.o(i.Serial_Init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    serial.o(i.Serial_Init) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    serial.o(i.Serial_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    serial.o(i.Serial_Init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    serial.o(i.Serial_Printf) refers to printfa.o(i.__0vsprintf) for vsprintf
    serial.o(i.Serial_Printf) refers to serial.o(i.Serial_SendString) for Serial_SendString
    serial.o(i.Serial_SendArray) refers to serial.o(i.Serial_SendByte) for Serial_SendByte
    serial.o(i.Serial_SendByte) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    serial.o(i.Serial_SendByte) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    serial.o(i.Serial_SendNumber) refers to serial.o(i.Pow) for Pow
    serial.o(i.Serial_SendNumber) refers to serial.o(i.Serial_SendByte) for Serial_SendByte
    serial.o(i.Serial_SendString) refers to serial.o(i.Serial_SendByte) for Serial_SendByte
    serial.o(i.Serial_SetPrintfUSART) refers to serial.o(.data) for .data
    serial.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    serial.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    serial.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    serial.o(i.USART1_IRQHandler) refers to serial.o(.data) for .data
    serial.o(i.USART1_IRQHandler) refers to serial.o(.bss) for .bss
    serial.o(i.USART2_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    serial.o(i.USART2_IRQHandler) refers to fifo.o(i.fifo_queueLength) for fifo_queueLength
    serial.o(i.USART2_IRQHandler) refers to fifo.o(i.fifo_In) for fifo_In
    serial.o(i.USART2_IRQHandler) refers to stm32f10x_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    serial.o(i.USART2_IRQHandler) refers to fifo.o(i.fifo_Out) for fifo_Out
    serial.o(i.USART2_IRQHandler) refers to serial.o(.data) for .data
    serial.o(i.USART2_IRQHandler) refers to serial.o(.bss) for .bss
    serial.o(i.USART3_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    serial.o(i.USART3_IRQHandler) refers to fifo.o(i.fifo_queueLength_3) for fifo_queueLength_3
    serial.o(i.USART3_IRQHandler) refers to fifo.o(i.fifo_In_3) for fifo_In_3
    serial.o(i.USART3_IRQHandler) refers to stm32f10x_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    serial.o(i.USART3_IRQHandler) refers to fifo.o(i.fifo_Out_3) for fifo_Out_3
    serial.o(i.USART3_IRQHandler) refers to serial.o(.data) for .data
    serial.o(i.USART3_IRQHandler) refers to serial.o(.bss) for .bss
    serial.o(i.fputc) refers to serial.o(i.Serial_SendByte) for Serial_SendByte
    serial.o(i.fputc) refers to serial.o(.data) for .data
    serial.o(.data) refers to serial.o(.bss) for buffer1
    serial.o(.data) refers to serial.o(.bss) for buffer2
    pid.o(i.pid_calculate_incremental) refers to pid.o(i.pid_formula_incremental) for pid_formula_incremental
    pid.o(i.pid_calculate_incremental) refers to pid.o(i.pid_out_limit) for pid_out_limit
    pid.o(i.pid_calculate_positional) refers to pid.o(i.pid_formula_positional) for pid_formula_positional
    pid.o(i.pid_calculate_positional) refers to pid.o(i.pid_out_limit) for pid_out_limit
    pid.o(i.pid_formula_incremental) refers to fadd.o(.text) for __aeabi_fsub
    pid.o(i.pid_formula_incremental) refers to fmul.o(.text) for __aeabi_fmul
    pid.o(i.pid_formula_incremental) refers to fscalb.o(.text) for __ARM_scalbnf
    pid.o(i.pid_formula_positional) refers to fadd.o(.text) for __aeabi_fsub
    pid.o(i.pid_formula_positional) refers to fmul.o(.text) for __aeabi_fmul
    pid.o(i.pid_out_limit) refers to cfrcmple.o(.text) for __aeabi_cfrcmple
    menu.o(i.CreateMenuItem) refers to malloc.o(i.malloc) for malloc
    menu.o(i.CreateMenuItem) refers to strcpy.o(.text) for strcpy
    menu.o(i.FreeSpace) refers to malloc.o(i.free) for free
    menu.o(i.MenuRuning) refers to key.o(i.Key_GetNum) for Key_GetNum
    menu.o(i.MenuRuning) refers to ha_oled.o(i.OLED_Printf) for OLED_Printf
    menu.o(i.MenuRuning) refers to ha_oled.o(i.OLED_Update) for OLED_Update
    menu.o(i.MenuRuning) refers to ha_oled.o(i.OLED_ReverseArea) for OLED_ReverseArea
    menu.o(i.MenuRuning) refers to ha_oled.o(i.OLED_ClearArea) for OLED_ClearArea
    menu.o(i.MenuRuning) refers to ha_oled.o(i.OLED_Clear) for OLED_Clear
    menu.o(i.MenuRuning) refers to menu.o(i.FreeSpace) for FreeSpace
    menu.o(i.MenuRuning) refers to menu.o(.data) for .data
    menu.o(i.Menu_Init) refers to menu.o(i.CreateMenuItem) for CreateMenuItem
    menu.o(i.Menu_Init) refers to menu.o(.data) for .data
    menu.o(i.Menu_Init) refers to menu.o(i.back) for back
    menu.o(i.Menu_Init) refers to menu.o(i.around) for around
    menu.o(i.Menu_Init) refers to menu.o(i.round_pro) for round_pro
    menu.o(i.Menu_Init) refers to menu.o(i.round_pro_max) for round_pro_max
    menu.o(i.around) refers to ha_oled.o(i.OLED_Clear) for OLED_Clear
    menu.o(i.around) refers to ha_oled.o(i.OLED_ShowString) for OLED_ShowString
    menu.o(i.around) refers to ha_oled.o(i.OLED_Update) for OLED_Update
    menu.o(i.around) refers to stepping_motor.o(i.Motor_Enable) for Motor_Enable
    menu.o(i.around) refers to delay.o(i.Delay_ms) for Delay_ms
    menu.o(i.around) refers to stepping_motor.o(i.Return_zero) for Return_zero
    menu.o(i.around) refers to stepping_motor.o(i.S_Control) for S_Control
    menu.o(i.around) refers to key.o(i.Key_GetNum) for Key_GetNum
    menu.o(i.back) refers to ha_oled.o(i.OLED_Clear) for OLED_Clear
    menu.o(i.back) refers to ha_oled.o(i.OLED_ShowString) for OLED_ShowString
    menu.o(i.back) refers to ha_oled.o(i.OLED_Update) for OLED_Update
    menu.o(i.back) refers to stepping_motor.o(i.Motor_Enable) for Motor_Enable
    menu.o(i.back) refers to delay.o(i.Delay_ms) for Delay_ms
    menu.o(i.back) refers to stepping_motor.o(i.Return_zero) for Return_zero
    menu.o(i.back) refers to key.o(i.Key_GetNum) for Key_GetNum
    menu.o(i.round_pro) refers to my_pid.o(i.app_pid_init) for app_pid_init
    menu.o(i.round_pro) refers to ha_oled.o(i.OLED_Clear) for OLED_Clear
    menu.o(i.round_pro) refers to ha_oled.o(i.OLED_ShowString) for OLED_ShowString
    menu.o(i.round_pro) refers to ha_oled.o(i.OLED_Update) for OLED_Update
    menu.o(i.round_pro) refers to delay.o(i.Delay_ms) for Delay_ms
    menu.o(i.round_pro) refers to stepping_motor.o(i.Motor_Enable) for Motor_Enable
    menu.o(i.round_pro) refers to key.o(i.Key_GetNum) for Key_GetNum
    menu.o(i.round_pro) refers to stepping_motor.o(i.Return_zero) for Return_zero
    menu.o(i.round_pro) refers to menu.o(.data) for .data
    menu.o(i.round_pro_max) refers to my_pid.o(i.app_pid_init) for app_pid_init
    menu.o(i.round_pro_max) refers to stepping_motor.o(i.Motor_Enable) for Motor_Enable
    menu.o(i.round_pro_max) refers to ha_oled.o(i.OLED_Clear) for OLED_Clear
    menu.o(i.round_pro_max) refers to ha_oled.o(i.OLED_ShowString) for OLED_ShowString
    menu.o(i.round_pro_max) refers to ha_oled.o(i.OLED_Update) for OLED_Update
    menu.o(i.round_pro_max) refers to key.o(i.Key_GetNum) for Key_GetNum
    menu.o(i.round_pro_max) refers to stepping_motor.o(i.Return_zero) for Return_zero
    menu.o(i.round_pro_max) refers to delay.o(i.Delay_ms) for Delay_ms
    menu.o(i.round_pro_max) refers to menu.o(.data) for .data
    my_pid.o(i.Target_practice) refers to serial.o(i.Get_light_data) for Get_light_data
    my_pid.o(i.Target_practice) refers to stepping_motor.o(i.Stop_Now) for Stop_Now
    my_pid.o(i.Target_practice) refers to ffixi.o(.text) for __aeabi_f2iz
    my_pid.o(i.Target_practice) refers to my_pid.o(i.app_pid_set_target) for app_pid_set_target
    my_pid.o(i.Target_practice) refers to my_pid.o(i.app_pid_update_position) for app_pid_update_position
    my_pid.o(i.Target_practice) refers to my_pid.o(i.app_pid_calc) for app_pid_calc
    my_pid.o(i.Target_practice) refers to fadd.o(.text) for __aeabi_fsub
    my_pid.o(i.Target_practice) refers to ha_oled.o(i.OLED_ShowString) for OLED_ShowString
    my_pid.o(i.Target_practice) refers to jg.o(i.JG_Ton) for JG_Ton
    my_pid.o(i.Target_practice) refers to ha_oled.o(i.OLED_Update) for OLED_Update
    my_pid.o(i.Target_practice) refers to jg.o(i.JG_Toff) for JG_Toff
    my_pid.o(i.Target_practice) refers to my_pid.o(.data) for .data
    my_pid.o(i.Target_practice_reset) refers to jg.o(i.JG_Toff) for JG_Toff
    my_pid.o(i.Target_practice_reset) refers to my_pid.o(.data) for .data
    my_pid.o(i.app_pid_calc) refers to fflti.o(.text) for __aeabi_i2f
    my_pid.o(i.app_pid_calc) refers to cfcmple.o(.text) for __aeabi_cfcmple
    my_pid.o(i.app_pid_calc) refers to stepping_motor.o(i.Stop_Now) for Stop_Now
    my_pid.o(i.app_pid_calc) refers to pid.o(i.pid_calculate_positional) for pid_calculate_positional
    my_pid.o(i.app_pid_calc) refers to my_pid.o(i.app_pid_limit_integral) for app_pid_limit_integral
    my_pid.o(i.app_pid_calc) refers to cfrcmple.o(.text) for __aeabi_cfrcmple
    my_pid.o(i.app_pid_calc) refers to ffixi.o(.text) for __aeabi_f2iz
    my_pid.o(i.app_pid_calc) refers to stepping_motor.o(i.Step_Motor_Set_Speed_my) for Step_Motor_Set_Speed_my
    my_pid.o(i.app_pid_calc) refers to my_pid.o(.data) for .data
    my_pid.o(i.app_pid_calc) refers to my_pid.o(.bss) for .bss
    my_pid.o(i.app_pid_init) refers to fflti.o(.text) for __aeabi_i2f
    my_pid.o(i.app_pid_init) refers to pid.o(i.pid_init) for pid_init
    my_pid.o(i.app_pid_init) refers to my_pid.o(.data) for .data
    my_pid.o(i.app_pid_init) refers to my_pid.o(.bss) for .bss
    my_pid.o(i.app_pid_limit_integral) refers to cfrcmple.o(.text) for __aeabi_cfrcmple
    my_pid.o(i.app_pid_limit_integral) refers to cfcmple.o(.text) for __aeabi_cfcmple
    my_pid.o(i.app_pid_set_target) refers to fflti.o(.text) for __aeabi_i2f
    my_pid.o(i.app_pid_set_target) refers to pid.o(i.pid_set_target) for pid_set_target
    my_pid.o(i.app_pid_set_target) refers to my_pid.o(.data) for .data
    my_pid.o(i.app_pid_set_target) refers to my_pid.o(.bss) for .bss
    my_pid.o(i.app_pid_update_position) refers to my_pid.o(.data) for .data
    jg.o(i.JG_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    jg.o(i.JG_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    jg.o(i.JG_Init) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    jg.o(i.JG_Toff) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    jg.o(i.JG_Ton) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    atan2.o(i.__softfp_atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.atan2) for atan2
    atan2.o(i.atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.atan2) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2.o(i.atan2) refers to dadd.o(.text) for __aeabi_dsub
    atan2.o(i.atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.__atan2$lsc) for __atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.__atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.__atan2$lsc) refers to errno.o(i.__set_errno) for __set_errno
    atan2_x.o(i.__atan2$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2_x.o(i.__atan2$lsc) refers to dadd.o(.text) for __aeabi_dsub
    atan2_x.o(i.__atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    round.o(i.round) refers (Special) to iusefp.o(.text) for __I$use$fp
    round.o(i.round) refers to drnd.o(.text) for _drnd
    round.o(i.round) refers to dadd.o(.text) for __aeabi_drsub
    round.o(i.round) refers to cdcmple.o(.text) for __aeabi_cdcmple
    round.o(i.round) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to serial.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to serial.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to serial.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to serial.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    malloc.o(i.free) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to startup_stm32f10x_md.o(HEAP) for __heap_base
    mallocr.o(i.__free$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.__malloc$realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocr.o(i.__malloc$realloc) refers to startup_stm32f10x_md.o(HEAP) for __heap_base
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocr.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.realloc) refers to mallocr.o(i.__free$realloc) for __free$realloc
    mallocr.o(i.realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.realloc) refers to mallocr.o(i.__malloc$realloc) for __malloc$realloc
    mallocr.o(i.realloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to startup_stm32f10x_md.o(HEAP) for __heap_base
    malloca.o(i.__free$memalign) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__malloc$memalign) refers to malloca.o(i.__aligned_malloc) for __aligned_malloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocra.o(i.__aligned_malloc$realloc) refers to startup_stm32f10x_md.o(HEAP) for __heap_base
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__free$realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__malloc$realloc$memalign) refers to mallocra.o(i.__aligned_malloc$realloc) for __aligned_malloc$realloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__free$realloc$memalign) for __free$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__malloc$realloc$memalign) for __malloc$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocra.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    fadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    fmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fscalb.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    fflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ffixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixui.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    cdcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    cfcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cfrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__softfp_atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.atan) for atan
    atan.o(i.atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.atan) refers to dadd.o(.text) for __aeabi_dadd
    atan.o(i.atan) refers to dscalb.o(.text) for __ARM_scalbn
    atan.o(i.atan) refers to ddiv.o(.text) for __aeabi_ddiv
    atan.o(i.atan) refers to dmul.o(.text) for __aeabi_dmul
    atan.o(i.atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.atan) refers to atan.o(.constdata) for .constdata
    atan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.__atan$lsc) for __atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.__atan$lsc) refers to dadd.o(.text) for __aeabi_dadd
    atan_x.o(i.__atan$lsc) refers to dscalb.o(.text) for __ARM_scalbn
    atan_x.o(i.__atan$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan_x.o(i.__atan$lsc) refers to dmul.o(.text) for __aeabi_dmul
    atan_x.o(i.__atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.__atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dscalb.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dscalb.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dscalb.o(.text) for __ARM_scalbn
    qnan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f10x_md.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f10x_md.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    drnd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    drnd.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    drnd.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.data), (1 bytes).
    Removing main.o(.data), (2 bytes).
    Removing main.o(.data), (1 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (1 bytes).
    Removing startup_stm32f10x_md.o(HEAP), (1024 bytes).
    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing system_stm32f10x.o(i.SystemCoreClockUpdate), (104 bytes).
    Removing system_stm32f10x.o(.data), (20 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (24 bytes).
    Removing misc.o(i.SysTick_CLKSourceConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogCmd), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_AutoInjectedConvCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearFlag), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_adc.o(i.ADC_Cmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_DMACmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_DeInit), (68 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeChannelCountConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigConvCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetCalibrationStatus), (14 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetConversionValue), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetDualModeConversionValue), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetFlagStatus), (14 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetITStatus), (28 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetInjectedConversionValue), (14 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus), (14 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartConvStatus), (14 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (14 bytes).
    Removing stm32f10x_adc.o(i.ADC_ITConfig), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_Init), (72 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedChannelConfig), (74 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedDiscModeCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedSequencerLengthConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_RegularChannelConfig), (116 bytes).
    Removing stm32f10x_adc.o(i.ADC_ResetCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_SetInjectedOffset), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartInjectedConvCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_StartCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_StructInit), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_TempSensorVrefintCmd), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearFlag), (16 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearITPendingBit), (16 bytes).
    Removing stm32f10x_bkp.o(i.BKP_DeInit), (18 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetFlagStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetITStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ITConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_RTCOutputConfig), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ReadBackupRegister), (16 bytes).
    Removing stm32f10x_bkp.o(i.BKP_SetRTCCalibrationValue), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinCmd), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinLevelConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_WriteBackupRegister), (16 bytes).
    Removing stm32f10x_can.o(i.CAN_CancelTransmit), (36 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearFlag), (48 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearITPendingBit), (120 bytes).
    Removing stm32f10x_can.o(i.CAN_DBGFreeze), (20 bytes).
    Removing stm32f10x_can.o(i.CAN_DeInit), (44 bytes).
    Removing stm32f10x_can.o(i.CAN_FIFORelease), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_FilterInit), (204 bytes).
    Removing stm32f10x_can.o(i.CAN_GetFlagStatus), (52 bytes).
    Removing stm32f10x_can.o(i.CAN_GetITStatus), (168 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLSBTransmitErrorCounter), (8 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLastErrorCode), (10 bytes).
    Removing stm32f10x_can.o(i.CAN_GetReceiveErrorCounter), (6 bytes).
    Removing stm32f10x_can.o(i.CAN_ITConfig), (16 bytes).
    Removing stm32f10x_can.o(i.CAN_Init), (232 bytes).
    Removing stm32f10x_can.o(i.CAN_MessagePending), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_OperatingModeRequest), (140 bytes).
    Removing stm32f10x_can.o(i.CAN_Receive), (142 bytes).
    Removing stm32f10x_can.o(i.CAN_SlaveStartBank), (44 bytes).
    Removing stm32f10x_can.o(i.CAN_Sleep), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_StructInit), (32 bytes).
    Removing stm32f10x_can.o(i.CAN_TTComModeCmd), (90 bytes).
    Removing stm32f10x_can.o(i.CAN_Transmit), (164 bytes).
    Removing stm32f10x_can.o(i.CAN_TransmitStatus), (108 bytes).
    Removing stm32f10x_can.o(i.CAN_WakeUp), (40 bytes).
    Removing stm32f10x_can.o(i.CheckITStatus), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearFlag), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearITPendingBit), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_Cmd), (28 bytes).
    Removing stm32f10x_cec.o(i.CEC_DeInit), (24 bytes).
    Removing stm32f10x_cec.o(i.CEC_EndOfMessageCmd), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetFlagStatus), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetITStatus), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_ITConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_Init), (24 bytes).
    Removing stm32f10x_cec.o(i.CEC_OwnAddressConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_ReceiveDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SendDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SetPrescaler), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_StartOfMessage), (16 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcBlockCRC), (28 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcCRC), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetCRC), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetIDRegister), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_ResetDR), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_SetIDRegister), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_Cmd), (24 bytes).
    Removing stm32f10x_dac.o(i.DAC_DMACmd), (24 bytes).
    Removing stm32f10x_dac.o(i.DAC_DeInit), (24 bytes).
    Removing stm32f10x_dac.o(i.DAC_DualSoftwareTriggerCmd), (28 bytes).
    Removing stm32f10x_dac.o(i.DAC_GetDataOutputValue), (24 bytes).
    Removing stm32f10x_dac.o(i.DAC_Init), (40 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel1Data), (20 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel2Data), (20 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetDualChannelData), (28 bytes).
    Removing stm32f10x_dac.o(i.DAC_SoftwareTriggerCmd), (28 bytes).
    Removing stm32f10x_dac.o(i.DAC_StructInit), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_WaveGenerationCmd), (24 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_Config), (24 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetDEVID), (16 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetREVID), (12 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearFlag), (24 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearITPendingBit), (24 bytes).
    Removing stm32f10x_dma.o(i.DMA_Cmd), (22 bytes).
    Removing stm32f10x_dma.o(i.DMA_DeInit), (228 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetCurrDataCounter), (6 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetFlagStatus), (32 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetITStatus), (32 bytes).
    Removing stm32f10x_dma.o(i.DMA_ITConfig), (16 bytes).
    Removing stm32f10x_dma.o(i.DMA_Init), (58 bytes).
    Removing stm32f10x_dma.o(i.DMA_SetCurrDataCounter), (4 bytes).
    Removing stm32f10x_dma.o(i.DMA_StructInit), (26 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetFlagStatus), (20 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetITStatus), (32 bytes).
    Removing stm32f10x_exti.o(i.EXTI_Init), (112 bytes).
    Removing stm32f10x_exti.o(i.EXTI_StructInit), (14 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ClearFlag), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EnableWriteProtection), (172 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages), (56 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllPages), (56 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseOptionBytes), (124 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ErasePage), (60 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetBank1Status), (40 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetFlagStatus), (32 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetPrefetchBufferStatus), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetStatus), (40 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetUserOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetWriteProtectionOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_HalfCycleAccessCmd), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ITConfig), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Lock), (16 bytes).
    Removing stm32f10x_flash.o(i.FLASH_LockBank1), (16 bytes).
    Removing stm32f10x_flash.o(i.FLASH_PrefetchBufferCmd), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramHalfWord), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramOptionByteData), (76 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramWord), (80 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ReadOutProtection), (136 bytes).
    Removing stm32f10x_flash.o(i.FLASH_SetLatency), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Unlock), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UnlockBank1), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UserOptionByteConfig), (96 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation), (34 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastOperation), (34 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearFlag), (38 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearITPendingBit), (42 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetECC), (18 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetFlagStatus), (40 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetITStatus), (48 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ITConfig), (72 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDCmd), (56 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDDeInit), (40 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDECCCmd), (56 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDInit), (104 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDStructInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMCmd), (32 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMDeInit), (38 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMInit), (200 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMStructInit), (98 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDCmd), (32 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDDeInit), (26 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDInit), (102 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDStructInit), (60 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (22 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (180 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EXTILineConfig), (40 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (28 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinRemapConfig), (88 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (6 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (6 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit), (14 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ARPCmd), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_AcknowledgeConfig), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CalculatePEC), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CheckEvent), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearFlag), (6 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearITPendingBit), (6 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Cmd), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMACmd), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMALastTransferCmd), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DeInit), (44 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DualAddressCmd), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_FastModeDutyCycleConfig), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GeneralCallCmd), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTART), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTOP), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetFlagStatus), (42 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetITStatus), (36 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetLastEvent), (14 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetPEC), (6 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ITConfig), (16 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Init), (188 bytes).
    Removing stm32f10x_i2c.o(i.I2C_NACKPositionConfig), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_OwnAddress2Config), (16 bytes).
    Removing stm32f10x_i2c.o(i.I2C_PECPositionConfig), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReadRegister), (10 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReceiveData), (6 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SMBusAlertConfig), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Send7bitAddress), (16 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SendData), (4 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SoftwareResetCmd), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StretchClockCmd), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StructInit), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_TransmitPEC), (20 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_Enable), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_GetFlagStatus), (20 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_ReloadCounter), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetPrescaler), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetReload), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_WriteAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_BackupAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_ClearFlag), (16 bytes).
    Removing stm32f10x_pwr.o(i.PWR_DeInit), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTANDBYMode), (40 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTOPMode), (52 bytes).
    Removing stm32f10x_pwr.o(i.PWR_GetFlagStatus), (20 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDLevelConfig), (20 bytes).
    Removing stm32f10x_pwr.o(i.PWR_WakeUpPinCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ADCCLKConfig), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_DeInit), (64 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetFlagStatus), (48 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HCLKConfig), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSEConfig), (52 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK1Config), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK2Config), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLConfig), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_SYSCLKConfig), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (44 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearFlag), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearITPendingBit), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_EnterConfigMode), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ExitConfigMode), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetCounter), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetDivider), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetFlagStatus), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetITStatus), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ITConfig), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetAlarm), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetCounter), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetPrescaler), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForLastTask), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForSynchro), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CEATAITCmd), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearFlag), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClockCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CmdStructInit), (14 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CommandCompletionCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DMACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataConfig), (44 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataStructInit), (20 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DeInit), (36 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetCommandResponse), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetDataCounter), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFIFOCount), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFlagStatus), (20 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetITStatus), (20 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetPowerState), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetResponse), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ITConfig), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_Init), (44 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ReadData), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCEATACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCommand), (40 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendSDIOSuspendCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetPowerState), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOOperation), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOReadWaitMode), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StartSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StopSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StructInit), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_WriteData), (12 bytes).
    Removing stm32f10x_spi.o(i.I2S_Cmd), (20 bytes).
    Removing stm32f10x_spi.o(i.I2S_Init), (150 bytes).
    Removing stm32f10x_spi.o(i.I2S_StructInit), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_BiDirectionalLineConfig), (22 bytes).
    Removing stm32f10x_spi.o(i.SPI_CalculateCRC), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_Cmd), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_DataSizeConfig), (16 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRC), (12 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRCPolynomial), (4 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearFlag), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearITPendingBit), (14 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DMACmd), (16 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DeInit), (84 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetFlagStatus), (14 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetITStatus), (44 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ITConfig), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ReceiveData), (4 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_SendData), (4 bytes).
    Removing stm32f10x_spi.o(i.SPI_Init), (56 bytes).
    Removing stm32f10x_spi.o(i.SPI_NSSInternalSoftwareConfig), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_SSOutputCmd), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_StructInit), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_TransmitCRC), (10 bytes).
    Removing stm32f10x_tim.o(i.TI1_Config), (108 bytes).
    Removing stm32f10x_tim.o(i.TI2_Config), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_ARRPreloadConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRConfig), (34 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCPreloadControl), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxCmd), (22 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxNCmd), (22 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC1Ref), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC2Ref), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC3Ref), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC4Ref), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_CounterModeConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_CtrlPWMOutputs), (22 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMACmd), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMAConfig), (8 bytes).
    Removing stm32f10x_tim.o(i.TIM_DeInit), (368 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode1Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode2Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig), (50 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC1Config), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC2Config), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC3Config), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC4Config), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture1), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture2), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture3), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture4), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCounter), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetFlagStatus), (14 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetPrescaler), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICInit), (300 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICStructInit), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1FastConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1Init), (128 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1NPolarityConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PolarityConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PreloadConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2FastConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2Init), (128 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2NPolarityConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PolarityConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PreloadConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3FastConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3Init), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3NPolarityConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PolarityConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PreloadConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4FastConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4Init), (100 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PolarityConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PreloadConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_PWMIConfig), (108 bytes).
    Removing stm32f10x_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCCDMA), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCOM), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectHallSensor), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectInputTrigger), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectMasterSlaveMode), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOCxM), (70 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOnePulseMode), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOutputTrigger), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectSlaveMode), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetClockDivision), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare1), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare3), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare4), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCounter), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC1Prescaler), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC2Prescaler), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC3Prescaler), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC4Prescaler), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_TIxExternalClockConfig), (46 bytes).
    Removing stm32f10x_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateDisableConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateRequestConfig), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearFlag), (6 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockInit), (30 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_DMACmd), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_DeInit), (116 bytes).
    Removing stm32f10x_usart.o(i.USART_HalfDuplexCmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDACmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDAConfig), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_LINBreakDetectLengthConfig), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_LINCmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_OneBitMethodCmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_OverSampling8Cmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiverWakeUpCmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_SetAddress), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardCmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardNACKCmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_StructInit), (22 bytes).
    Removing stm32f10x_usart.o(i.USART_WakeUpConfig), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_ClearFlag), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_DeInit), (24 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_Enable), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_EnableIT), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_GetFlagStatus), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetCounter), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetPrescaler), (20 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetWindowValue), (28 bytes).
    Removing delay.o(i.Delay_s), (22 bytes).
    Removing fifo.o(i.fifo_isEmpty), (28 bytes).
    Removing fifo.o(i.fifo_isEmpty_3), (28 bytes).
    Removing ha_oled.o(i.Ha_IIC_SendAck), (22 bytes).
    Removing ha_oled.o(i.Ha_ReceiveByte), (48 bytes).
    Removing ha_oled.o(i.OLED_DrawArc), (582 bytes).
    Removing ha_oled.o(i.OLED_DrawCircle), (318 bytes).
    Removing ha_oled.o(i.OLED_DrawEllipse), (652 bytes).
    Removing ha_oled.o(i.OLED_DrawLine), (234 bytes).
    Removing ha_oled.o(i.OLED_DrawPoint), (52 bytes).
    Removing ha_oled.o(i.OLED_DrawRectangle), (116 bytes).
    Removing ha_oled.o(i.OLED_DrawTriangle), (198 bytes).
    Removing ha_oled.o(i.OLED_GetPoint), (56 bytes).
    Removing ha_oled.o(i.OLED_IsInAngle), (96 bytes).
    Removing ha_oled.o(i.OLED_Pow), (14 bytes).
    Removing ha_oled.o(i.OLED_Reverse), (40 bytes).
    Removing ha_oled.o(i.OLED_ReverseArea), (84 bytes).
    Removing ha_oled.o(i.OLED_ShowBinNum), (64 bytes).
    Removing ha_oled.o(i.OLED_ShowChinese), (148 bytes).
    Removing ha_oled.o(i.OLED_ShowFloatNum), (190 bytes).
    Removing ha_oled.o(i.OLED_ShowHexNum), (72 bytes).
    Removing ha_oled.o(i.OLED_ShowNum), (72 bytes).
    Removing ha_oled.o(i.OLED_ShowSignedNum), (92 bytes).
    Removing ha_oled.o(i.OLED_UpdateArea), (96 bytes).
    Removing ha_oled.o(i.OLED_pnpoly), (114 bytes).
    Removing ha_oleddata.o(.constdata), (32 bytes).
    Removing ha_oleddata.o(.constdata), (200 bytes).
    Removing stepping_motor.o(i.Change_tozero_parameter), (132 bytes).
    Removing stepping_motor.o(i.Contact_LockedRotor_Protect), (46 bytes).
    Removing stepping_motor.o(i.Interrupt_return_zero), (46 bytes).
    Removing stepping_motor.o(i.Many_Motor), (46 bytes).
    Removing stepping_motor.o(i.Modify_Ctrl_Mode), (56 bytes).
    Removing stepping_motor.o(i.Motor_Set_Speed), (124 bytes).
    Removing stepping_motor.o(i.Read_Params), (142 bytes).
    Removing stepping_motor.o(i.Reset_To_Zero), (46 bytes).
    Removing stepping_motor.o(i.S_Control), (104 bytes).
    Removing stepping_motor.o(i.Step_Motor_Set_Pwm), (84 bytes).
    Removing serial.o(i.Get_packet_type), (12 bytes).
    Removing serial.o(i.Pow), (14 bytes).
    Removing serial.o(i.Serial_GetRxflag_1), (24 bytes).
    Removing serial.o(i.Serial_Printf), (34 bytes).
    Removing serial.o(i.Serial_SendNumber), (56 bytes).
    Removing serial.o(i.Serial_SendString), (28 bytes).
    Removing serial.o(i.Serial_SetPrintfUSART), (12 bytes).
    Removing serial.o(i.fputc), (24 bytes).
    Removing serial.o(.data), (4 bytes).
    Removing pid.o(i.pid_calculate_incremental), (20 bytes).
    Removing pid.o(i.pid_formula_incremental), (108 bytes).
    Removing pid.o(i.pid_reset), (18 bytes).
    Removing pid.o(i.pid_set_limit), (4 bytes).
    Removing pid.o(i.pid_set_params), (6 bytes).
    Removing menu.o(i.CreateMenuItem), (52 bytes).
    Removing menu.o(i.FreeSpace), (20 bytes).
    Removing menu.o(i.MenuRuning), (424 bytes).
    Removing menu.o(i.Menu_Init), (264 bytes).
    Removing menu.o(i.around), (392 bytes).
    Removing menu.o(i.back), (144 bytes).
    Removing menu.o(i.round_pro), (268 bytes).
    Removing menu.o(i.round_pro_max), (164 bytes).
    Removing my_pid.o(i.Target_practice_reset), (20 bytes).
    Removing fscalb.o(.text), (24 bytes).
    Removing dflti.o(.text), (34 bytes).
    Removing dfltui.o(.text), (26 bytes).
    Removing dfixi.o(.text), (62 bytes).
    Removing dfixui.o(.text), (50 bytes).
    Removing cdcmple.o(.text), (48 bytes).
    Removing d2f.o(.text), (56 bytes).
    Removing drnd.o(.text), (136 bytes).
    Removing dscalb.o(.text), (46 bytes).

523 unused section(s) (total 22129 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloca.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloc.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocr.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocra.o ABSOLUTE
    ../clib/microlib/malloc/mvars.c          0x00000000   Number         0  mvars.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strcmp.c         0x00000000   Number         0  strcmp.o ABSOLUTE
    ../clib/microlib/string/strcpy.c         0x00000000   Number         0  strcpy.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  fadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixui.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixui.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  fflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fprnd.c                0x00000000   Number         0  drnd.o ABSOLUTE
    ../fplib/microlib/fpscalb.c              0x00000000   Number         0  dscalb.o ABSOLUTE
    ../fplib/microlib/fpscalb.c              0x00000000   Number         0  fscalb.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/round.c                       0x00000000   Number         0  round.o ABSOLUTE
    Hardware\Ha_OLED.c                       0x00000000   Number         0  ha_oled.o ABSOLUTE
    Hardware\Ha_OLEDData.c                   0x00000000   Number         0  ha_oleddata.o ABSOLUTE
    Hardware\JG.c                            0x00000000   Number         0  jg.o ABSOLUTE
    Hardware\KEY.c                           0x00000000   Number         0  key.o ABSOLUTE
    Hardware\Menu.c                          0x00000000   Number         0  menu.o ABSOLUTE
    Hardware\My_pid.c                        0x00000000   Number         0  my_pid.o ABSOLUTE
    Hardware\PID.c                           0x00000000   Number         0  pid.o ABSOLUTE
    Hardware\Serial.c                        0x00000000   Number         0  serial.o ABSOLUTE
    Hardware\stepping_motor.c                0x00000000   Number         0  stepping_motor.o ABSOLUTE
    Library\misc.c                           0x00000000   Number         0  misc.o ABSOLUTE
    Library\stm32f10x_adc.c                  0x00000000   Number         0  stm32f10x_adc.o ABSOLUTE
    Library\stm32f10x_bkp.c                  0x00000000   Number         0  stm32f10x_bkp.o ABSOLUTE
    Library\stm32f10x_can.c                  0x00000000   Number         0  stm32f10x_can.o ABSOLUTE
    Library\stm32f10x_cec.c                  0x00000000   Number         0  stm32f10x_cec.o ABSOLUTE
    Library\stm32f10x_crc.c                  0x00000000   Number         0  stm32f10x_crc.o ABSOLUTE
    Library\stm32f10x_dac.c                  0x00000000   Number         0  stm32f10x_dac.o ABSOLUTE
    Library\stm32f10x_dbgmcu.c               0x00000000   Number         0  stm32f10x_dbgmcu.o ABSOLUTE
    Library\stm32f10x_dma.c                  0x00000000   Number         0  stm32f10x_dma.o ABSOLUTE
    Library\stm32f10x_exti.c                 0x00000000   Number         0  stm32f10x_exti.o ABSOLUTE
    Library\stm32f10x_flash.c                0x00000000   Number         0  stm32f10x_flash.o ABSOLUTE
    Library\stm32f10x_fsmc.c                 0x00000000   Number         0  stm32f10x_fsmc.o ABSOLUTE
    Library\stm32f10x_gpio.c                 0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    Library\stm32f10x_i2c.c                  0x00000000   Number         0  stm32f10x_i2c.o ABSOLUTE
    Library\stm32f10x_iwdg.c                 0x00000000   Number         0  stm32f10x_iwdg.o ABSOLUTE
    Library\stm32f10x_pwr.c                  0x00000000   Number         0  stm32f10x_pwr.o ABSOLUTE
    Library\stm32f10x_rcc.c                  0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    Library\stm32f10x_rtc.c                  0x00000000   Number         0  stm32f10x_rtc.o ABSOLUTE
    Library\stm32f10x_sdio.c                 0x00000000   Number         0  stm32f10x_sdio.o ABSOLUTE
    Library\stm32f10x_spi.c                  0x00000000   Number         0  stm32f10x_spi.o ABSOLUTE
    Library\stm32f10x_tim.c                  0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    Library\stm32f10x_usart.c                0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    Library\stm32f10x_wwdg.c                 0x00000000   Number         0  stm32f10x_wwdg.o ABSOLUTE
    Start_file\\core_cm3.c                   0x00000000   Number         0  core_cm3.o ABSOLUTE
    Start_file\core_cm3.c                    0x00000000   Number         0  core_cm3.o ABSOLUTE
    Start_file\startup_stm32f10x_md.s        0x00000000   Number         0  startup_stm32f10x_md.o ABSOLUTE
    Start_file\system_stm32f10x.c            0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    System\Delay.c                           0x00000000   Number         0  delay.o ABSOLUTE
    System\Timer.c                           0x00000000   Number         0  timer.o ABSOLUTE
    System\fifo.c                            0x00000000   Number         0  fifo.o ABSOLUTE
    User\main.c                              0x00000000   Number         0  main.o ABSOLUTE
    User\stm32f10x_it.c                      0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    cdcmple.s                                0x00000000   Number         0  cdcmple.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    cfcmple.s                                0x00000000   Number         0  cfcmple.o ABSOLUTE
    cfrcmple.s                               0x00000000   Number         0  cfrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08000000   Section      236  startup_stm32f10x_md.o(RESET)
    .ARM.Collect$$$$00000000                 0x080000ec   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x080000ec   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x080000f0   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x080000f4   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x080000f4   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x080000f4   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x080000fc   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x080000fc   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x080000fc   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x080000fc   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x08000100   Section       36  startup_stm32f10x_md.o(.text)
    .text                                    0x08000124   Section        0  strcmp.o(.text)
    .text                                    0x08000140   Section        0  fadd.o(.text)
    .text                                    0x080001f0   Section        0  fmul.o(.text)
    .text                                    0x08000254   Section        0  dadd.o(.text)
    .text                                    0x080003a2   Section        0  dmul.o(.text)
    .text                                    0x08000486   Section        0  ddiv.o(.text)
    .text                                    0x08000564   Section        0  fflti.o(.text)
    .text                                    0x08000576   Section        0  ffixi.o(.text)
    .text                                    0x080005a8   Section        0  ffixui.o(.text)
    .text                                    0x080005d0   Section       48  cdrcmple.o(.text)
    .text                                    0x08000600   Section       20  cfcmple.o(.text)
    .text                                    0x08000614   Section       20  cfrcmple.o(.text)
    .text                                    0x08000628   Section        0  uidiv.o(.text)
    .text                                    0x08000654   Section        0  uldiv.o(.text)
    .text                                    0x080006b6   Section        0  llshl.o(.text)
    .text                                    0x080006d4   Section        0  llushr.o(.text)
    .text                                    0x080006f4   Section        0  llsshr.o(.text)
    .text                                    0x08000718   Section        0  iusefp.o(.text)
    .text                                    0x08000718   Section        0  fepilogue.o(.text)
    .text                                    0x08000786   Section        0  depilogue.o(.text)
    .text                                    0x08000840   Section        0  dfixul.o(.text)
    .text                                    0x08000870   Section       36  init.o(.text)
    i.BusFault_Handler                       0x08000894   Section        0  stm32f10x_it.o(i.BusFault_Handler)
    i.DebugMon_Handler                       0x08000896   Section        0  stm32f10x_it.o(i.DebugMon_Handler)
    i.Delay_ms                               0x08000898   Section        0  delay.o(i.Delay_ms)
    i.Delay_us                               0x080008ae   Section        0  delay.o(i.Delay_us)
    i.DoubleBuffer_Init                      0x080008d0   Section        0  serial.o(i.DoubleBuffer_Init)
    i.GPIO_Init                              0x080008e8   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.GPIO_ReadInputDataBit                  0x08000984   Section        0  stm32f10x_gpio.o(i.GPIO_ReadInputDataBit)
    i.GPIO_ResetBits                         0x08000992   Section        0  stm32f10x_gpio.o(i.GPIO_ResetBits)
    i.GPIO_SetBits                           0x08000996   Section        0  stm32f10x_gpio.o(i.GPIO_SetBits)
    i.GPIO_WriteBit                          0x0800099a   Section        0  stm32f10x_gpio.o(i.GPIO_WriteBit)
    i.Get_light_data                         0x080009a4   Section        0  serial.o(i.Get_light_data)
    i.Ha_IIC_Init                            0x080009f0   Section        0  ha_oled.o(i.Ha_IIC_Init)
    i.Ha_IIC_R_SDA                           0x08000a28   Section        0  ha_oled.o(i.Ha_IIC_R_SDA)
    i.Ha_IIC_ReceiveAck                      0x08000a38   Section        0  ha_oled.o(i.Ha_IIC_ReceiveAck)
    i.Ha_IIC_Start                           0x08000a56   Section        0  ha_oled.o(i.Ha_IIC_Start)
    i.Ha_IIC_Stop                            0x08000a74   Section        0  ha_oled.o(i.Ha_IIC_Stop)
    i.Ha_IIC_W_SCL                           0x08000a8c   Section        0  ha_oled.o(i.Ha_IIC_W_SCL)
    i.Ha_IIC_W_SDA                           0x08000a9c   Section        0  ha_oled.o(i.Ha_IIC_W_SDA)
    i.Ha_SendByte                            0x08000aac   Section        0  ha_oled.o(i.Ha_SendByte)
    i.HardFault_Handler                      0x08000ad4   Section        0  stm32f10x_it.o(i.HardFault_Handler)
    i.Init_Queue                             0x08000ad8   Section        0  fifo.o(i.Init_Queue)
    i.Init_Queue_3                           0x08000aec   Section        0  fifo.o(i.Init_Queue_3)
    i.JG_Init                                0x08000b00   Section        0  jg.o(i.JG_Init)
    i.JG_Toff                                0x08000b38   Section        0  jg.o(i.JG_Toff)
    i.JG_Ton                                 0x08000b48   Section        0  jg.o(i.JG_Ton)
    i.KEY_Init                               0x08000b58   Section        0  key.o(i.KEY_Init)
    i.Key_GetNum                             0x08000ba4   Section        0  key.o(i.Key_GetNum)
    i.Key_Getstate                           0x08000bb4   Section        0  key.o(i.Key_Getstate)
    i.MemManage_Handler                      0x08000bf8   Section        0  stm32f10x_it.o(i.MemManage_Handler)
    i.Motor_Enable                           0x08000bfa   Section        0  stepping_motor.o(i.Motor_Enable)
    i.My_motor_Init                          0x08000c34   Section        0  stepping_motor.o(i.My_motor_Init)
    i.NMI_Handler                            0x08000c5c   Section        0  stm32f10x_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x08000c60   Section        0  misc.o(i.NVIC_Init)
    i.NVIC_PriorityGroupConfig               0x08000cc4   Section        0  misc.o(i.NVIC_PriorityGroupConfig)
    i.OLED_Clear                             0x08000cd8   Section        0  ha_oled.o(i.OLED_Clear)
    i.OLED_ClearArea                         0x08000d00   Section        0  ha_oled.o(i.OLED_ClearArea)
    i.OLED_Init                              0x08000d54   Section        0  ha_oled.o(i.OLED_Init)
    i.OLED_Printf                            0x08000df0   Section        0  ha_oled.o(i.OLED_Printf)
    i.OLED_SetCursor                         0x08000e1a   Section        0  ha_oled.o(i.OLED_SetCursor)
    i.OLED_ShowChar                          0x08000e3c   Section        0  ha_oled.o(i.OLED_ShowChar)
    i.OLED_ShowImage                         0x08000e78   Section        0  ha_oled.o(i.OLED_ShowImage)
    i.OLED_ShowString                        0x08000f2c   Section        0  ha_oled.o(i.OLED_ShowString)
    i.OLED_Update                            0x08001088   Section        0  ha_oled.o(i.OLED_Update)
    i.OLED_WriteCommand                      0x080010b0   Section        0  ha_oled.o(i.OLED_WriteCommand)
    i.OLED_WriteData                         0x080010de   Section        0  ha_oled.o(i.OLED_WriteData)
    i.PendSV_Handler                         0x0800111a   Section        0  stm32f10x_it.o(i.PendSV_Handler)
    i.RCC_APB1PeriphClockCmd                 0x0800111c   Section        0  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x08001134   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_GetClocksFreq                      0x0800114c   Section        0  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    i.Return_zero                            0x080011e0   Section        0  stepping_motor.o(i.Return_zero)
    i.SVC_Handler                            0x08001212   Section        0  stm32f10x_it.o(i.SVC_Handler)
    i.Serial_GetRxflag_2                     0x08001214   Section        0  serial.o(i.Serial_GetRxflag_2)
    i.Serial_GetRxflag_3                     0x0800122c   Section        0  serial.o(i.Serial_GetRxflag_3)
    i.Serial_Init                            0x08001244   Section        0  serial.o(i.Serial_Init)
    i.Serial_SendArray                       0x080013fc   Section        0  serial.o(i.Serial_SendArray)
    i.Serial_SendByte                        0x0800141e   Section        0  serial.o(i.Serial_SendByte)
    i.SetSysClockTo72                        0x08001434   Section        0  system_stm32f10x.o(i.SetSysClockTo72)
    SetSysClockTo72                          0x08001435   Thumb Code   160  system_stm32f10x.o(i.SetSysClockTo72)
    i.Set_zero_position                      0x080014dc   Section        0  stepping_motor.o(i.Set_zero_position)
    i.Step_Motor_Set_Speed_my                0x08001510   Section        0  stepping_motor.o(i.Step_Motor_Set_Speed_my)
    i.Stop_Now                               0x080015c0   Section        0  stepping_motor.o(i.Stop_Now)
    i.SysTick_Handler                        0x080015f4   Section        0  stm32f10x_it.o(i.SysTick_Handler)
    i.SystemInit                             0x080015f8   Section        0  system_stm32f10x.o(i.SystemInit)
    i.TIM2_IRQHandler                        0x08001648   Section        0  main.o(i.TIM2_IRQHandler)
    i.TIM_ClearFlag                          0x0800167c   Section        0  stm32f10x_tim.o(i.TIM_ClearFlag)
    i.TIM_ClearITPendingBit                  0x08001682   Section        0  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    i.TIM_Cmd                                0x08001688   Section        0  stm32f10x_tim.o(i.TIM_Cmd)
    i.TIM_GetITStatus                        0x0800169c   Section        0  stm32f10x_tim.o(i.TIM_GetITStatus)
    i.TIM_ITConfig                           0x080016b4   Section        0  stm32f10x_tim.o(i.TIM_ITConfig)
    i.TIM_InternalClockConfig                0x080016c4   Section        0  stm32f10x_tim.o(i.TIM_InternalClockConfig)
    i.TIM_TimeBaseInit                       0x080016d0   Section        0  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    i.Target_practice                        0x0800176c   Section        0  my_pid.o(i.Target_practice)
    i.Timer_Init                             0x08001894   Section        0  timer.o(i.Timer_Init)
    i.USART1_IRQHandler                      0x0800190c   Section        0  serial.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x080019d0   Section        0  serial.o(i.USART2_IRQHandler)
    i.USART3_IRQHandler                      0x08001a54   Section        0  serial.o(i.USART3_IRQHandler)
    i.USART_ClearITPendingBit                0x08001ad8   Section        0  stm32f10x_usart.o(i.USART_ClearITPendingBit)
    i.USART_Cmd                              0x08001ae4   Section        0  stm32f10x_usart.o(i.USART_Cmd)
    i.USART_GetFlagStatus                    0x08001af8   Section        0  stm32f10x_usart.o(i.USART_GetFlagStatus)
    i.USART_GetITStatus                      0x08001b06   Section        0  stm32f10x_usart.o(i.USART_GetITStatus)
    i.USART_ITConfig                         0x08001b44   Section        0  stm32f10x_usart.o(i.USART_ITConfig)
    i.USART_Init                             0x08001b74   Section        0  stm32f10x_usart.o(i.USART_Init)
    i.USART_ReceiveData                      0x08001c20   Section        0  stm32f10x_usart.o(i.USART_ReceiveData)
    i.USART_SendData                         0x08001c28   Section        0  stm32f10x_usart.o(i.USART_SendData)
    i.UsageFault_Handler                     0x08001c30   Section        0  stm32f10x_it.o(i.UsageFault_Handler)
    i.V_Control                              0x08001c32   Section        0  stepping_motor.o(i.V_Control)
    i.__0vsprintf                            0x08001c78   Section        0  printfa.o(i.__0vsprintf)
    i.__scatterload_copy                     0x08001c9c   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08001caa   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08001cac   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._fp_digits                             0x08001cbc   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x08001cbd   Thumb Code   366  printfa.o(i._fp_digits)
    i._printf_core                           0x08001e40   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x08001e41   Thumb Code  1744  printfa.o(i._printf_core)
    i._printf_post_padding                   0x0800251c   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x0800251d   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08002540   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08002541   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._sputc                                 0x0800256e   Section        0  printfa.o(i._sputc)
    _sputc                                   0x0800256f   Thumb Code    10  printfa.o(i._sputc)
    i.app_pid_calc                           0x08002578   Section        0  my_pid.o(i.app_pid_calc)
    i.app_pid_init                           0x0800267c   Section        0  my_pid.o(i.app_pid_init)
    i.app_pid_limit_integral                 0x080026bc   Section        0  my_pid.o(i.app_pid_limit_integral)
    app_pid_limit_integral                   0x080026bd   Thumb Code    36  my_pid.o(i.app_pid_limit_integral)
    i.app_pid_set_target                     0x080026e0   Section        0  my_pid.o(i.app_pid_set_target)
    i.app_pid_update_position                0x08002710   Section        0  my_pid.o(i.app_pid_update_position)
    i.fifo_In                                0x0800272c   Section        0  fifo.o(i.fifo_In)
    i.fifo_In_3                              0x08002754   Section        0  fifo.o(i.fifo_In_3)
    i.fifo_Out                               0x0800277c   Section        0  fifo.o(i.fifo_Out)
    i.fifo_Out_3                             0x080027a4   Section        0  fifo.o(i.fifo_Out_3)
    i.fifo_queueLength                       0x080027cc   Section        0  fifo.o(i.fifo_queueLength)
    i.fifo_queueLength_3                     0x080027fc   Section        0  fifo.o(i.fifo_queueLength_3)
    i.key_Loop                               0x0800282c   Section        0  key.o(i.key_Loop)
    i.main                                   0x08002874   Section        0  main.o(i.main)
    i.pid_calculate_positional               0x08002c28   Section        0  pid.o(i.pid_calculate_positional)
    i.pid_formula_positional                 0x08002c3c   Section        0  pid.o(i.pid_formula_positional)
    pid_formula_positional                   0x08002c3d   Thumb Code    94  pid.o(i.pid_formula_positional)
    i.pid_init                               0x08002c9a   Section        0  pid.o(i.pid_init)
    i.pid_out_limit                          0x08002cb8   Section        0  pid.o(i.pid_out_limit)
    pid_out_limit                            0x08002cb9   Thumb Code    38  pid.o(i.pid_out_limit)
    i.pid_set_target                         0x08002cde   Section        0  pid.o(i.pid_set_target)
    .constdata                               0x08002ce2   Section     1520  ha_oleddata.o(.constdata)
    .constdata                               0x080032d2   Section      570  ha_oleddata.o(.constdata)
    .constdata                               0x0800350c   Section      666  ha_oleddata.o(.constdata)
    .data                                    0x20000000   Section       20  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x20000000   Data           4  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x20000004   Data          16  stm32f10x_rcc.o(.data)
    .data                                    0x20000014   Section        3  key.o(.data)
    NowState                                 0x20000014   Data           1  key.o(.data)
    LastState                                0x20000015   Data           1  key.o(.data)
    .data                                    0x20000018   Section       24  serial.o(.data)
    Rxstate                                  0x2000001c   Data           1  serial.o(.data)
    temp                                     0x2000001d   Data           1  serial.o(.data)
    current_packet_type                      0x2000001e   Data           1  serial.o(.data)
    expected_data_length                     0x2000001f   Data           1  serial.o(.data)
    active_buffer                            0x20000024   Data           4  serial.o(.data)
    processing_buffer                        0x20000028   Data           4  serial.o(.data)
    g_printf_usart                           0x2000002c   Data           4  serial.o(.data)
    .data                                    0x20000030   Section        8  menu.o(.data)
    .data                                    0x20000038   Section       92  my_pid.o(.data)
    target_state                             0x20000038   Data           1  my_pid.o(.data)
    target_reached                           0x20000039   Data           1  my_pid.o(.data)
    fire_timer                               0x2000003c   Data           4  my_pid.o(.data)
    status_display_timer                     0x20000040   Data           4  my_pid.o(.data)
    .bss                                     0x20000094   Section      516  fifo.o(.bss)
    .bss                                     0x20000298   Section     1024  ha_oled.o(.bss)
    .bss                                     0x20000698   Section      297  serial.o(.bss)
    .bss                                     0x200007c1   Section       36  serial.o(.bss)
    buffer1                                  0x200007c1   Data          36  serial.o(.bss)
    .bss                                     0x200007e5   Section       36  serial.o(.bss)
    buffer2                                  0x200007e5   Data          36  serial.o(.bss)
    .bss                                     0x2000080c   Section      120  my_pid.o(.bss)
    STACK                                    0x20000888   Section     1024  startup_stm32f10x_md.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x000000ec   Number         0  startup_stm32f10x_md.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_md.o(RESET)
    __Vectors_End                            0x080000ec   Data           0  startup_stm32f10x_md.o(RESET)
    __main                                   0x080000ed   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x080000ed   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x080000f1   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x080000f5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x080000f5   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x080000f5   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x080000f5   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x080000fd   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x080000fd   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x08000101   Thumb Code     8  startup_stm32f10x_md.o(.text)
    ADC1_2_IRQHandler                        0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_RX1_IRQHandler                      0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_SCE_IRQHandler                      0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel1_IRQHandler                 0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel2_IRQHandler                 0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel3_IRQHandler                 0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel4_IRQHandler                 0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel5_IRQHandler                 0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel6_IRQHandler                 0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel7_IRQHandler                 0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI0_IRQHandler                         0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI15_10_IRQHandler                     0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI1_IRQHandler                         0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI2_IRQHandler                         0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI3_IRQHandler                         0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI4_IRQHandler                         0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI9_5_IRQHandler                       0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    FLASH_IRQHandler                         0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_ER_IRQHandler                       0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_EV_IRQHandler                       0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_ER_IRQHandler                       0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_EV_IRQHandler                       0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    PVD_IRQHandler                           0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RCC_IRQHandler                           0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTCAlarm_IRQHandler                      0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTC_IRQHandler                           0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI1_IRQHandler                          0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI2_IRQHandler                          0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TAMPER_IRQHandler                        0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_BRK_IRQHandler                      0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_CC_IRQHandler                       0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_UP_IRQHandler                       0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM3_IRQHandler                          0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM4_IRQHandler                          0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USBWakeUp_IRQHandler                     0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    WWDG_IRQHandler                          0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    strcmp                                   0x08000125   Thumb Code    28  strcmp.o(.text)
    __aeabi_fadd                             0x08000141   Thumb Code   164  fadd.o(.text)
    __aeabi_fsub                             0x080001e5   Thumb Code     6  fadd.o(.text)
    __aeabi_frsub                            0x080001eb   Thumb Code     6  fadd.o(.text)
    __aeabi_fmul                             0x080001f1   Thumb Code   100  fmul.o(.text)
    __aeabi_dadd                             0x08000255   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x08000397   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x0800039d   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x080003a3   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x08000487   Thumb Code   222  ddiv.o(.text)
    __aeabi_i2f                              0x08000565   Thumb Code    18  fflti.o(.text)
    __aeabi_f2iz                             0x08000577   Thumb Code    50  ffixi.o(.text)
    __aeabi_f2uiz                            0x080005a9   Thumb Code    40  ffixui.o(.text)
    __aeabi_cdrcmple                         0x080005d1   Thumb Code    48  cdrcmple.o(.text)
    __aeabi_cfcmpeq                          0x08000601   Thumb Code     0  cfcmple.o(.text)
    __aeabi_cfcmple                          0x08000601   Thumb Code    20  cfcmple.o(.text)
    __aeabi_cfrcmple                         0x08000615   Thumb Code    20  cfrcmple.o(.text)
    __aeabi_uidiv                            0x08000629   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x08000629   Thumb Code    44  uidiv.o(.text)
    __aeabi_uldivmod                         0x08000655   Thumb Code    98  uldiv.o(.text)
    __aeabi_llsl                             0x080006b7   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x080006b7   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x080006d5   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x080006d5   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x080006f5   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x080006f5   Thumb Code     0  llsshr.o(.text)
    __I$use$fp                               0x08000719   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x08000719   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x0800072b   Thumb Code    92  fepilogue.o(.text)
    _double_round                            0x08000787   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x080007a5   Thumb Code   156  depilogue.o(.text)
    __aeabi_d2ulz                            0x08000841   Thumb Code    48  dfixul.o(.text)
    __scatterload                            0x08000871   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08000871   Thumb Code     0  init.o(.text)
    BusFault_Handler                         0x08000895   Thumb Code     2  stm32f10x_it.o(i.BusFault_Handler)
    DebugMon_Handler                         0x08000897   Thumb Code     2  stm32f10x_it.o(i.DebugMon_Handler)
    Delay_ms                                 0x08000899   Thumb Code    22  delay.o(i.Delay_ms)
    Delay_us                                 0x080008af   Thumb Code    32  delay.o(i.Delay_us)
    DoubleBuffer_Init                        0x080008d1   Thumb Code    18  serial.o(i.DoubleBuffer_Init)
    GPIO_Init                                0x080008e9   Thumb Code   156  stm32f10x_gpio.o(i.GPIO_Init)
    GPIO_ReadInputDataBit                    0x08000985   Thumb Code    14  stm32f10x_gpio.o(i.GPIO_ReadInputDataBit)
    GPIO_ResetBits                           0x08000993   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_ResetBits)
    GPIO_SetBits                             0x08000997   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_SetBits)
    GPIO_WriteBit                            0x0800099b   Thumb Code    10  stm32f10x_gpio.o(i.GPIO_WriteBit)
    Get_light_data                           0x080009a5   Thumb Code    68  serial.o(i.Get_light_data)
    Ha_IIC_Init                              0x080009f1   Thumb Code    50  ha_oled.o(i.Ha_IIC_Init)
    Ha_IIC_R_SDA                             0x08000a29   Thumb Code    10  ha_oled.o(i.Ha_IIC_R_SDA)
    Ha_IIC_ReceiveAck                        0x08000a39   Thumb Code    30  ha_oled.o(i.Ha_IIC_ReceiveAck)
    Ha_IIC_Start                             0x08000a57   Thumb Code    30  ha_oled.o(i.Ha_IIC_Start)
    Ha_IIC_Stop                              0x08000a75   Thumb Code    24  ha_oled.o(i.Ha_IIC_Stop)
    Ha_IIC_W_SCL                             0x08000a8d   Thumb Code    12  ha_oled.o(i.Ha_IIC_W_SCL)
    Ha_IIC_W_SDA                             0x08000a9d   Thumb Code    12  ha_oled.o(i.Ha_IIC_W_SDA)
    Ha_SendByte                              0x08000aad   Thumb Code    40  ha_oled.o(i.Ha_SendByte)
    HardFault_Handler                        0x08000ad5   Thumb Code     2  stm32f10x_it.o(i.HardFault_Handler)
    Init_Queue                               0x08000ad9   Thumb Code    14  fifo.o(i.Init_Queue)
    Init_Queue_3                             0x08000aed   Thumb Code    14  fifo.o(i.Init_Queue_3)
    JG_Init                                  0x08000b01   Thumb Code    50  jg.o(i.JG_Init)
    JG_Toff                                  0x08000b39   Thumb Code    10  jg.o(i.JG_Toff)
    JG_Ton                                   0x08000b49   Thumb Code    10  jg.o(i.JG_Ton)
    KEY_Init                                 0x08000b59   Thumb Code    68  key.o(i.KEY_Init)
    Key_GetNum                               0x08000ba5   Thumb Code    10  key.o(i.Key_GetNum)
    Key_Getstate                             0x08000bb5   Thumb Code    58  key.o(i.Key_Getstate)
    MemManage_Handler                        0x08000bf9   Thumb Code     2  stm32f10x_it.o(i.MemManage_Handler)
    Motor_Enable                             0x08000bfb   Thumb Code    56  stepping_motor.o(i.Motor_Enable)
    My_motor_Init                            0x08000c35   Thumb Code    30  stepping_motor.o(i.My_motor_Init)
    NMI_Handler                              0x08000c5d   Thumb Code     2  stm32f10x_it.o(i.NMI_Handler)
    NVIC_Init                                0x08000c61   Thumb Code    94  misc.o(i.NVIC_Init)
    NVIC_PriorityGroupConfig                 0x08000cc5   Thumb Code    10  misc.o(i.NVIC_PriorityGroupConfig)
    OLED_Clear                               0x08000cd9   Thumb Code    34  ha_oled.o(i.OLED_Clear)
    OLED_ClearArea                           0x08000d01   Thumb Code    80  ha_oled.o(i.OLED_ClearArea)
    OLED_Init                                0x08000d55   Thumb Code   156  ha_oled.o(i.OLED_Init)
    OLED_Printf                              0x08000df1   Thumb Code    42  ha_oled.o(i.OLED_Printf)
    OLED_SetCursor                           0x08000e1b   Thumb Code    34  ha_oled.o(i.OLED_SetCursor)
    OLED_ShowChar                            0x08000e3d   Thumb Code    52  ha_oled.o(i.OLED_ShowChar)
    OLED_ShowImage                           0x08000e79   Thumb Code   174  ha_oled.o(i.OLED_ShowImage)
    OLED_ShowString                          0x08000f2d   Thumb Code   340  ha_oled.o(i.OLED_ShowString)
    OLED_Update                              0x08001089   Thumb Code    34  ha_oled.o(i.OLED_Update)
    OLED_WriteCommand                        0x080010b1   Thumb Code    46  ha_oled.o(i.OLED_WriteCommand)
    OLED_WriteData                           0x080010df   Thumb Code    60  ha_oled.o(i.OLED_WriteData)
    PendSV_Handler                           0x0800111b   Thumb Code     2  stm32f10x_it.o(i.PendSV_Handler)
    RCC_APB1PeriphClockCmd                   0x0800111d   Thumb Code    18  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x08001135   Thumb Code    18  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_GetClocksFreq                        0x0800114d   Thumb Code   132  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    Return_zero                              0x080011e1   Thumb Code    50  stepping_motor.o(i.Return_zero)
    SVC_Handler                              0x08001213   Thumb Code     2  stm32f10x_it.o(i.SVC_Handler)
    Serial_GetRxflag_2                       0x08001215   Thumb Code    18  serial.o(i.Serial_GetRxflag_2)
    Serial_GetRxflag_3                       0x0800122d   Thumb Code    18  serial.o(i.Serial_GetRxflag_3)
    Serial_Init                              0x08001245   Thumb Code   420  serial.o(i.Serial_Init)
    Serial_SendArray                         0x080013fd   Thumb Code    34  serial.o(i.Serial_SendArray)
    Serial_SendByte                          0x0800141f   Thumb Code    22  serial.o(i.Serial_SendByte)
    Set_zero_position                        0x080014dd   Thumb Code    52  stepping_motor.o(i.Set_zero_position)
    Step_Motor_Set_Speed_my                  0x08001511   Thumb Code   152  stepping_motor.o(i.Step_Motor_Set_Speed_my)
    Stop_Now                                 0x080015c1   Thumb Code    52  stepping_motor.o(i.Stop_Now)
    SysTick_Handler                          0x080015f5   Thumb Code     2  stm32f10x_it.o(i.SysTick_Handler)
    SystemInit                               0x080015f9   Thumb Code    64  system_stm32f10x.o(i.SystemInit)
    TIM2_IRQHandler                          0x08001649   Thumb Code    46  main.o(i.TIM2_IRQHandler)
    TIM_ClearFlag                            0x0800167d   Thumb Code     6  stm32f10x_tim.o(i.TIM_ClearFlag)
    TIM_ClearITPendingBit                    0x08001683   Thumb Code     6  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    TIM_Cmd                                  0x08001689   Thumb Code    20  stm32f10x_tim.o(i.TIM_Cmd)
    TIM_GetITStatus                          0x0800169d   Thumb Code    24  stm32f10x_tim.o(i.TIM_GetITStatus)
    TIM_ITConfig                             0x080016b5   Thumb Code    16  stm32f10x_tim.o(i.TIM_ITConfig)
    TIM_InternalClockConfig                  0x080016c5   Thumb Code    10  stm32f10x_tim.o(i.TIM_InternalClockConfig)
    TIM_TimeBaseInit                         0x080016d1   Thumb Code   114  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    Target_practice                          0x0800176d   Thumb Code   270  my_pid.o(i.Target_practice)
    Timer_Init                               0x08001895   Thumb Code   120  timer.o(i.Timer_Init)
    USART1_IRQHandler                        0x0800190d   Thumb Code   182  serial.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x080019d1   Thumb Code   118  serial.o(i.USART2_IRQHandler)
    USART3_IRQHandler                        0x08001a55   Thumb Code   118  serial.o(i.USART3_IRQHandler)
    USART_ClearITPendingBit                  0x08001ad9   Thumb Code    12  stm32f10x_usart.o(i.USART_ClearITPendingBit)
    USART_Cmd                                0x08001ae5   Thumb Code    20  stm32f10x_usart.o(i.USART_Cmd)
    USART_GetFlagStatus                      0x08001af9   Thumb Code    14  stm32f10x_usart.o(i.USART_GetFlagStatus)
    USART_GetITStatus                        0x08001b07   Thumb Code    62  stm32f10x_usart.o(i.USART_GetITStatus)
    USART_ITConfig                           0x08001b45   Thumb Code    48  stm32f10x_usart.o(i.USART_ITConfig)
    USART_Init                               0x08001b75   Thumb Code   166  stm32f10x_usart.o(i.USART_Init)
    USART_ReceiveData                        0x08001c21   Thumb Code     8  stm32f10x_usart.o(i.USART_ReceiveData)
    USART_SendData                           0x08001c29   Thumb Code     8  stm32f10x_usart.o(i.USART_SendData)
    UsageFault_Handler                       0x08001c31   Thumb Code     2  stm32f10x_it.o(i.UsageFault_Handler)
    V_Control                                0x08001c33   Thumb Code    68  stepping_motor.o(i.V_Control)
    __0vsprintf                              0x08001c79   Thumb Code    30  printfa.o(i.__0vsprintf)
    __1vsprintf                              0x08001c79   Thumb Code     0  printfa.o(i.__0vsprintf)
    __2vsprintf                              0x08001c79   Thumb Code     0  printfa.o(i.__0vsprintf)
    __c89vsprintf                            0x08001c79   Thumb Code     0  printfa.o(i.__0vsprintf)
    vsprintf                                 0x08001c79   Thumb Code     0  printfa.o(i.__0vsprintf)
    __scatterload_copy                       0x08001c9d   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08001cab   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08001cad   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    app_pid_calc                             0x08002579   Thumb Code   244  my_pid.o(i.app_pid_calc)
    app_pid_init                             0x0800267d   Thumb Code    56  my_pid.o(i.app_pid_init)
    app_pid_set_target                       0x080026e1   Thumb Code    40  my_pid.o(i.app_pid_set_target)
    app_pid_update_position                  0x08002711   Thumb Code    22  my_pid.o(i.app_pid_update_position)
    fifo_In                                  0x0800272d   Thumb Code    36  fifo.o(i.fifo_In)
    fifo_In_3                                0x08002755   Thumb Code    36  fifo.o(i.fifo_In_3)
    fifo_Out                                 0x0800277d   Thumb Code    36  fifo.o(i.fifo_Out)
    fifo_Out_3                               0x080027a5   Thumb Code    36  fifo.o(i.fifo_Out_3)
    fifo_queueLength                         0x080027cd   Thumb Code    44  fifo.o(i.fifo_queueLength)
    fifo_queueLength_3                       0x080027fd   Thumb Code    44  fifo.o(i.fifo_queueLength_3)
    key_Loop                                 0x0800282d   Thumb Code    66  key.o(i.key_Loop)
    main                                     0x08002875   Thumb Code   838  main.o(i.main)
    pid_calculate_positional                 0x08002c29   Thumb Code    20  pid.o(i.pid_calculate_positional)
    pid_init                                 0x08002c9b   Thumb Code    30  pid.o(i.pid_init)
    pid_set_target                           0x08002cdf   Thumb Code     4  pid.o(i.pid_set_target)
    OLED_F8x16                               0x08002ce2   Data        1520  ha_oleddata.o(.constdata)
    OLED_F6x8                                0x080032d2   Data         570  ha_oleddata.o(.constdata)
    OLED_CF16x16                             0x0800350c   Data         666  ha_oleddata.o(.constdata)
    Region$$Table$$Base                      0x080037a8   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080037c8   Number         0  anon$$obj.o(Region$$Table)
    KeyNum                                   0x20000016   Data           1  key.o(.data)
    rxFrameFlag                              0x20000018   Data           1  serial.o(.data)
    rxCount                                  0x20000019   Data           1  serial.o(.data)
    rxFrameFlag_3                            0x2000001a   Data           1  serial.o(.data)
    rxCount_3                                0x2000001b   Data           1  serial.o(.data)
    ByteRecv                                 0x20000020   Data           1  serial.o(.data)
    Rx_flag                                  0x20000021   Data           1  serial.o(.data)
    Mode                                     0x20000030   Data           1  menu.o(.data)
    head                                     0x20000034   Data           4  menu.o(.data)
    motor_x                                  0x2000003a   Data           1  my_pid.o(.data)
    motor_y                                  0x2000003b   Data           1  my_pid.o(.data)
    target_x                                 0x20000044   Data           4  my_pid.o(.data)
    target_y                                 0x20000048   Data           4  my_pid.o(.data)
    current_x                                0x2000004c   Data           4  my_pid.o(.data)
    current_y                                0x20000050   Data           4  my_pid.o(.data)
    pid_params_x                             0x20000054   Data          32  my_pid.o(.data)
    pid_params_y                             0x20000074   Data          32  my_pid.o(.data)
    rxFIFO                                   0x20000094   Data         258  fifo.o(.bss)
    rxFIFO_3                                 0x20000196   Data         258  fifo.o(.bss)
    OLED_DisplayBuf                          0x20000298   Data        1024  ha_oled.o(.bss)
    rx_double_buffer                         0x20000698   Data          41  serial.o(.bss)
    rxCmd                                    0x200006c1   Data         128  serial.o(.bss)
    rxCmd_3                                  0x20000741   Data         128  serial.o(.bss)
    pid_x                                    0x2000080c   Data          60  my_pid.o(.bss)
    pid_y                                    0x20000848   Data          60  my_pid.o(.bss)
    __initial_sp                             0x20000c88   Data           0  startup_stm32f10x_md.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000ed

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000385c, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x000037c8, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000000ec   Data   RO          240    RESET               startup_stm32f10x_md.o
    0x080000ec   0x080000ec   0x00000000   Code   RO         4228  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x080000ec   0x080000ec   0x00000004   Code   RO         4354    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x080000f0   0x080000f0   0x00000004   Code   RO         4357    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x080000f4   0x080000f4   0x00000000   Code   RO         4359    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x080000f4   0x080000f4   0x00000000   Code   RO         4361    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x080000f4   0x080000f4   0x00000008   Code   RO         4362    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x080000fc   0x080000fc   0x00000000   Code   RO         4364    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x080000fc   0x080000fc   0x00000000   Code   RO         4366    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x080000fc   0x080000fc   0x00000004   Code   RO         4355    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x08000100   0x08000100   0x00000024   Code   RO          241    .text               startup_stm32f10x_md.o
    0x08000124   0x08000124   0x0000001c   Code   RO         4233    .text               mc_w.l(strcmp.o)
    0x08000140   0x08000140   0x000000b0   Code   RO         4293    .text               mf_w.l(fadd.o)
    0x080001f0   0x080001f0   0x00000064   Code   RO         4295    .text               mf_w.l(fmul.o)
    0x08000254   0x08000254   0x0000014e   Code   RO         4299    .text               mf_w.l(dadd.o)
    0x080003a2   0x080003a2   0x000000e4   Code   RO         4301    .text               mf_w.l(dmul.o)
    0x08000486   0x08000486   0x000000de   Code   RO         4303    .text               mf_w.l(ddiv.o)
    0x08000564   0x08000564   0x00000012   Code   RO         4305    .text               mf_w.l(fflti.o)
    0x08000576   0x08000576   0x00000032   Code   RO         4311    .text               mf_w.l(ffixi.o)
    0x080005a8   0x080005a8   0x00000028   Code   RO         4313    .text               mf_w.l(ffixui.o)
    0x080005d0   0x080005d0   0x00000030   Code   RO         4321    .text               mf_w.l(cdrcmple.o)
    0x08000600   0x08000600   0x00000014   Code   RO         4325    .text               mf_w.l(cfcmple.o)
    0x08000614   0x08000614   0x00000014   Code   RO         4327    .text               mf_w.l(cfrcmple.o)
    0x08000628   0x08000628   0x0000002c   Code   RO         4369    .text               mc_w.l(uidiv.o)
    0x08000654   0x08000654   0x00000062   Code   RO         4371    .text               mc_w.l(uldiv.o)
    0x080006b6   0x080006b6   0x0000001e   Code   RO         4373    .text               mc_w.l(llshl.o)
    0x080006d4   0x080006d4   0x00000020   Code   RO         4375    .text               mc_w.l(llushr.o)
    0x080006f4   0x080006f4   0x00000024   Code   RO         4377    .text               mc_w.l(llsshr.o)
    0x08000718   0x08000718   0x00000000   Code   RO         4392    .text               mc_w.l(iusefp.o)
    0x08000718   0x08000718   0x0000006e   Code   RO         4393    .text               mf_w.l(fepilogue.o)
    0x08000786   0x08000786   0x000000ba   Code   RO         4395    .text               mf_w.l(depilogue.o)
    0x08000840   0x08000840   0x00000030   Code   RO         4399    .text               mf_w.l(dfixul.o)
    0x08000870   0x08000870   0x00000024   Code   RO         4405    .text               mc_w.l(init.o)
    0x08000894   0x08000894   0x00000002   Code   RO          175    i.BusFault_Handler  stm32f10x_it.o
    0x08000896   0x08000896   0x00000002   Code   RO          176    i.DebugMon_Handler  stm32f10x_it.o
    0x08000898   0x08000898   0x00000016   Code   RO         3322    i.Delay_ms          delay.o
    0x080008ae   0x080008ae   0x00000020   Code   RO         3324    i.Delay_us          delay.o
    0x080008ce   0x080008ce   0x00000002   PAD
    0x080008d0   0x080008d0   0x00000018   Code   RO         3861    i.DoubleBuffer_Init  serial.o
    0x080008e8   0x080008e8   0x0000009c   Code   RO         1489    i.GPIO_Init         stm32f10x_gpio.o
    0x08000984   0x08000984   0x0000000e   Code   RO         1493    i.GPIO_ReadInputDataBit  stm32f10x_gpio.o
    0x08000992   0x08000992   0x00000004   Code   RO         1496    i.GPIO_ResetBits    stm32f10x_gpio.o
    0x08000996   0x08000996   0x00000004   Code   RO         1497    i.GPIO_SetBits      stm32f10x_gpio.o
    0x0800099a   0x0800099a   0x0000000a   Code   RO         1500    i.GPIO_WriteBit     stm32f10x_gpio.o
    0x080009a4   0x080009a4   0x0000004c   Code   RO         3862    i.Get_light_data    serial.o
    0x080009f0   0x080009f0   0x00000038   Code   RO         3429    i.Ha_IIC_Init       ha_oled.o
    0x08000a28   0x08000a28   0x00000010   Code   RO         3430    i.Ha_IIC_R_SDA      ha_oled.o
    0x08000a38   0x08000a38   0x0000001e   Code   RO         3431    i.Ha_IIC_ReceiveAck  ha_oled.o
    0x08000a56   0x08000a56   0x0000001e   Code   RO         3433    i.Ha_IIC_Start      ha_oled.o
    0x08000a74   0x08000a74   0x00000018   Code   RO         3434    i.Ha_IIC_Stop       ha_oled.o
    0x08000a8c   0x08000a8c   0x00000010   Code   RO         3435    i.Ha_IIC_W_SCL      ha_oled.o
    0x08000a9c   0x08000a9c   0x00000010   Code   RO         3436    i.Ha_IIC_W_SDA      ha_oled.o
    0x08000aac   0x08000aac   0x00000028   Code   RO         3438    i.Ha_SendByte       ha_oled.o
    0x08000ad4   0x08000ad4   0x00000002   Code   RO          177    i.HardFault_Handler  stm32f10x_it.o
    0x08000ad6   0x08000ad6   0x00000002   PAD
    0x08000ad8   0x08000ad8   0x00000014   Code   RO         3357    i.Init_Queue        fifo.o
    0x08000aec   0x08000aec   0x00000014   Code   RO         3358    i.Init_Queue_3      fifo.o
    0x08000b00   0x08000b00   0x00000038   Code   RO         4194    i.JG_Init           jg.o
    0x08000b38   0x08000b38   0x00000010   Code   RO         4195    i.JG_Toff           jg.o
    0x08000b48   0x08000b48   0x00000010   Code   RO         4196    i.JG_Ton            jg.o
    0x08000b58   0x08000b58   0x0000004c   Code   RO         3710    i.KEY_Init          key.o
    0x08000ba4   0x08000ba4   0x00000010   Code   RO         3711    i.Key_GetNum        key.o
    0x08000bb4   0x08000bb4   0x00000044   Code   RO         3712    i.Key_Getstate      key.o
    0x08000bf8   0x08000bf8   0x00000002   Code   RO          178    i.MemManage_Handler  stm32f10x_it.o
    0x08000bfa   0x08000bfa   0x00000038   Code   RO         3751    i.Motor_Enable      stepping_motor.o
    0x08000c32   0x08000c32   0x00000002   PAD
    0x08000c34   0x08000c34   0x00000028   Code   RO         3753    i.My_motor_Init     stepping_motor.o
    0x08000c5c   0x08000c5c   0x00000002   Code   RO          179    i.NMI_Handler       stm32f10x_it.o
    0x08000c5e   0x08000c5e   0x00000002   PAD
    0x08000c60   0x08000c60   0x00000064   Code   RO          304    i.NVIC_Init         misc.o
    0x08000cc4   0x08000cc4   0x00000014   Code   RO          305    i.NVIC_PriorityGroupConfig  misc.o
    0x08000cd8   0x08000cd8   0x00000028   Code   RO         3439    i.OLED_Clear        ha_oled.o
    0x08000d00   0x08000d00   0x00000054   Code   RO         3440    i.OLED_ClearArea    ha_oled.o
    0x08000d54   0x08000d54   0x0000009c   Code   RO         3449    i.OLED_Init         ha_oled.o
    0x08000df0   0x08000df0   0x0000002a   Code   RO         3452    i.OLED_Printf       ha_oled.o
    0x08000e1a   0x08000e1a   0x00000022   Code   RO         3455    i.OLED_SetCursor    ha_oled.o
    0x08000e3c   0x08000e3c   0x0000003c   Code   RO         3457    i.OLED_ShowChar     ha_oled.o
    0x08000e78   0x08000e78   0x000000b4   Code   RO         3461    i.OLED_ShowImage    ha_oled.o
    0x08000f2c   0x08000f2c   0x0000015c   Code   RO         3464    i.OLED_ShowString   ha_oled.o
    0x08001088   0x08001088   0x00000028   Code   RO         3465    i.OLED_Update       ha_oled.o
    0x080010b0   0x080010b0   0x0000002e   Code   RO         3467    i.OLED_WriteCommand  ha_oled.o
    0x080010de   0x080010de   0x0000003c   Code   RO         3468    i.OLED_WriteData    ha_oled.o
    0x0800111a   0x0800111a   0x00000002   Code   RO          180    i.PendSV_Handler    stm32f10x_it.o
    0x0800111c   0x0800111c   0x00000018   Code   RO         1917    i.RCC_APB1PeriphClockCmd  stm32f10x_rcc.o
    0x08001134   0x08001134   0x00000018   Code   RO         1919    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x0800114c   0x0800114c   0x00000094   Code   RO         1927    i.RCC_GetClocksFreq  stm32f10x_rcc.o
    0x080011e0   0x080011e0   0x00000032   Code   RO         3756    i.Return_zero       stepping_motor.o
    0x08001212   0x08001212   0x00000002   Code   RO          181    i.SVC_Handler       stm32f10x_it.o
    0x08001214   0x08001214   0x00000018   Code   RO         3866    i.Serial_GetRxflag_2  serial.o
    0x0800122c   0x0800122c   0x00000018   Code   RO         3867    i.Serial_GetRxflag_3  serial.o
    0x08001244   0x08001244   0x000001b8   Code   RO         3868    i.Serial_Init       serial.o
    0x080013fc   0x080013fc   0x00000022   Code   RO         3870    i.Serial_SendArray  serial.o
    0x0800141e   0x0800141e   0x00000016   Code   RO         3871    i.Serial_SendByte   serial.o
    0x08001434   0x08001434   0x000000a8   Code   RO          256    i.SetSysClockTo72   system_stm32f10x.o
    0x080014dc   0x080014dc   0x00000034   Code   RO         3758    i.Set_zero_position  stepping_motor.o
    0x08001510   0x08001510   0x000000b0   Code   RO         3760    i.Step_Motor_Set_Speed_my  stepping_motor.o
    0x080015c0   0x080015c0   0x00000034   Code   RO         3761    i.Stop_Now          stepping_motor.o
    0x080015f4   0x080015f4   0x00000002   Code   RO          182    i.SysTick_Handler   stm32f10x_it.o
    0x080015f6   0x080015f6   0x00000002   PAD
    0x080015f8   0x080015f8   0x00000050   Code   RO          258    i.SystemInit        system_stm32f10x.o
    0x08001648   0x08001648   0x00000034   Code   RO            1    i.TIM2_IRQHandler   main.o
    0x0800167c   0x0800167c   0x00000006   Code   RO         2554    i.TIM_ClearFlag     stm32f10x_tim.o
    0x08001682   0x08001682   0x00000006   Code   RO         2555    i.TIM_ClearITPendingBit  stm32f10x_tim.o
    0x08001688   0x08001688   0x00000014   Code   RO         2560    i.TIM_Cmd           stm32f10x_tim.o
    0x0800169c   0x0800169c   0x00000018   Code   RO         2581    i.TIM_GetITStatus   stm32f10x_tim.o
    0x080016b4   0x080016b4   0x00000010   Code   RO         2585    i.TIM_ITConfig      stm32f10x_tim.o
    0x080016c4   0x080016c4   0x0000000a   Code   RO         2587    i.TIM_InternalClockConfig  stm32f10x_tim.o
    0x080016ce   0x080016ce   0x00000002   PAD
    0x080016d0   0x080016d0   0x0000009c   Code   RO         2631    i.TIM_TimeBaseInit  stm32f10x_tim.o
    0x0800176c   0x0800176c   0x00000128   Code   RO         4136    i.Target_practice   my_pid.o
    0x08001894   0x08001894   0x00000078   Code   RO         3345    i.Timer_Init        timer.o
    0x0800190c   0x0800190c   0x000000c4   Code   RO         3875    i.USART1_IRQHandler  serial.o
    0x080019d0   0x080019d0   0x00000084   Code   RO         3876    i.USART2_IRQHandler  serial.o
    0x08001a54   0x08001a54   0x00000084   Code   RO         3877    i.USART3_IRQHandler  serial.o
    0x08001ad8   0x08001ad8   0x0000000c   Code   RO         3083    i.USART_ClearITPendingBit  stm32f10x_usart.o
    0x08001ae4   0x08001ae4   0x00000014   Code   RO         3086    i.USART_Cmd         stm32f10x_usart.o
    0x08001af8   0x08001af8   0x0000000e   Code   RO         3089    i.USART_GetFlagStatus  stm32f10x_usart.o
    0x08001b06   0x08001b06   0x0000003e   Code   RO         3090    i.USART_GetITStatus  stm32f10x_usart.o
    0x08001b44   0x08001b44   0x00000030   Code   RO         3092    i.USART_ITConfig    stm32f10x_usart.o
    0x08001b74   0x08001b74   0x000000ac   Code   RO         3093    i.USART_Init        stm32f10x_usart.o
    0x08001c20   0x08001c20   0x00000008   Code   RO         3100    i.USART_ReceiveData  stm32f10x_usart.o
    0x08001c28   0x08001c28   0x00000008   Code   RO         3103    i.USART_SendData    stm32f10x_usart.o
    0x08001c30   0x08001c30   0x00000002   Code   RO          183    i.UsageFault_Handler  stm32f10x_it.o
    0x08001c32   0x08001c32   0x00000044   Code   RO         3762    i.V_Control         stepping_motor.o
    0x08001c76   0x08001c76   0x00000002   PAD
    0x08001c78   0x08001c78   0x00000024   Code   RO         4244    i.__0vsprintf       mc_w.l(printfa.o)
    0x08001c9c   0x08001c9c   0x0000000e   Code   RO         4411    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08001caa   0x08001caa   0x00000002   Code   RO         4412    i.__scatterload_null  mc_w.l(handlers.o)
    0x08001cac   0x08001cac   0x0000000e   Code   RO         4413    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x08001cba   0x08001cba   0x00000002   PAD
    0x08001cbc   0x08001cbc   0x00000184   Code   RO         4245    i._fp_digits        mc_w.l(printfa.o)
    0x08001e40   0x08001e40   0x000006dc   Code   RO         4246    i._printf_core      mc_w.l(printfa.o)
    0x0800251c   0x0800251c   0x00000024   Code   RO         4247    i._printf_post_padding  mc_w.l(printfa.o)
    0x08002540   0x08002540   0x0000002e   Code   RO         4248    i._printf_pre_padding  mc_w.l(printfa.o)
    0x0800256e   0x0800256e   0x0000000a   Code   RO         4250    i._sputc            mc_w.l(printfa.o)
    0x08002578   0x08002578   0x00000104   Code   RO         4138    i.app_pid_calc      my_pid.o
    0x0800267c   0x0800267c   0x00000040   Code   RO         4139    i.app_pid_init      my_pid.o
    0x080026bc   0x080026bc   0x00000024   Code   RO         4140    i.app_pid_limit_integral  my_pid.o
    0x080026e0   0x080026e0   0x00000030   Code   RO         4141    i.app_pid_set_target  my_pid.o
    0x08002710   0x08002710   0x0000001c   Code   RO         4142    i.app_pid_update_position  my_pid.o
    0x0800272c   0x0800272c   0x00000028   Code   RO         3359    i.fifo_In           fifo.o
    0x08002754   0x08002754   0x00000028   Code   RO         3360    i.fifo_In_3         fifo.o
    0x0800277c   0x0800277c   0x00000028   Code   RO         3361    i.fifo_Out          fifo.o
    0x080027a4   0x080027a4   0x00000028   Code   RO         3362    i.fifo_Out_3        fifo.o
    0x080027cc   0x080027cc   0x00000030   Code   RO         3365    i.fifo_queueLength  fifo.o
    0x080027fc   0x080027fc   0x00000030   Code   RO         3366    i.fifo_queueLength_3  fifo.o
    0x0800282c   0x0800282c   0x00000048   Code   RO         3713    i.key_Loop          key.o
    0x08002874   0x08002874   0x000003b4   Code   RO            2    i.main              main.o
    0x08002c28   0x08002c28   0x00000014   Code   RO         3988    i.pid_calculate_positional  pid.o
    0x08002c3c   0x08002c3c   0x0000005e   Code   RO         3990    i.pid_formula_positional  pid.o
    0x08002c9a   0x08002c9a   0x0000001e   Code   RO         3991    i.pid_init          pid.o
    0x08002cb8   0x08002cb8   0x00000026   Code   RO         3992    i.pid_out_limit     pid.o
    0x08002cde   0x08002cde   0x00000004   Code   RO         3996    i.pid_set_target    pid.o
    0x08002ce2   0x08002ce2   0x000005f0   Data   RO         3694    .constdata          ha_oleddata.o
    0x080032d2   0x080032d2   0x0000023a   Data   RO         3695    .constdata          ha_oleddata.o
    0x0800350c   0x0800350c   0x0000029a   Data   RO         3696    .constdata          ha_oleddata.o
    0x080037a6   0x080037a6   0x00000002   PAD
    0x080037a8   0x080037a8   0x00000020   Data   RO         4409    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x080037c8, Size: 0x00000c88, Max: 0x00005000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x080037c8   0x00000014   Data   RW         1947    .data               stm32f10x_rcc.o
    0x20000014   0x080037dc   0x00000003   Data   RW         3714    .data               key.o
    0x20000017   0x080037df   0x00000001   PAD
    0x20000018   0x080037e0   0x00000018   Data   RW         3882    .data               serial.o
    0x20000030   0x080037f8   0x00000008   Data   RW         4061    .data               menu.o
    0x20000038   0x08003800   0x0000005c   Data   RW         4144    .data               my_pid.o
    0x20000094        -       0x00000204   Zero   RW         3367    .bss                fifo.o
    0x20000298        -       0x00000400   Zero   RW         3470    .bss                ha_oled.o
    0x20000698        -       0x00000129   Zero   RW         3879    .bss                serial.o
    0x200007c1        -       0x00000024   Zero   RW         3880    .bss                serial.o
    0x200007e5        -       0x00000024   Zero   RW         3881    .bss                serial.o
    0x20000809   0x0800385c   0x00000003   PAD
    0x2000080c        -       0x00000078   Zero   RW         4143    .bss                my_pid.o
    0x20000884   0x0800385c   0x00000004   PAD
    0x20000888        -       0x00000400   Zero   RW          238    STACK               startup_stm32f10x_md.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

        54          0          0          0          0       1128   delay.o
       296         36          0          0        516       4240   fifo.o
      1318         58          0          0       1024      12260   ha_oled.o
         0          0       2756          0          0       1062   ha_oleddata.o
        88         18          0          0          0       1169   jg.o
       232         30          0          3          0       2083   key.o
      1000        116          0          0          0     253033   main.o
         0          0          0          8          0       1768   menu.o
       120         16          0          0          0       2991   misc.o
       732         64          0         92        120       4529   my_pid.o
       186          0          0          0          0       3203   pid.o
      1104         94          0         24        369       7516   serial.o
        36          8        236          0       1024        804   startup_stm32f10x_md.o
       494         34          0          0          0       5643   stepping_motor.o
       188          0          0          0          0      12260   stm32f10x_gpio.o
        18          0          0          0          0       3506   stm32f10x_it.o
       196         28          0         20          0      12914   stm32f10x_rcc.o
       238         42          0          0          0      25415   stm32f10x_tim.o
       344          6          0          0          0      12419   stm32f10x_usart.o
       248         24          0          0          0       1453   system_stm32f10x.o
       120          0          0          0          0        533   timer.o

    ----------------------------------------------------------------------
      7026        <USER>       <GROUP>        148       3060     369929   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        14          0          2          1          7          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
      2272         86          0          0          0        516   printfa.o
        28          0          0          0          0         76   strcmp.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdrcmple.o
        20          0          0          0          0         68   cfcmple.o
        20          0          0          0          0         68   cfrcmple.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        48          0          0          0          0         68   dfixul.o
       228          0          0          0          0         96   dmul.o
       176          0          0          0          0        140   fadd.o
       110          0          0          0          0        168   fepilogue.o
        50          0          0          0          0         68   ffixi.o
        40          0          0          0          0         68   ffixui.o
        18          0          0          0          0         68   fflti.o
       100          0          0          0          0         76   fmul.o

    ----------------------------------------------------------------------
      4228        <USER>          <GROUP>          0          0       2416   Library Totals
         2          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2626        102          0          0          0       1036   mc_w.l
      1600          0          0          0          0       1380   mf_w.l

    ----------------------------------------------------------------------
      4228        <USER>          <GROUP>          0          0       2416   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     11254        676       3026        148       3060     365997   Grand Totals
     11254        676       3026        148       3060     365997   ELF Image Totals
     11254        676       3026        148          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                14280 (  13.95kB)
    Total RW  Size (RW Data + ZI Data)              3208 (   3.13kB)
    Total ROM Size (Code + RO Data + RW Data)      14428 (  14.09kB)

==============================================================================

