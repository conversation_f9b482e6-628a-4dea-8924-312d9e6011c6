/*
时钟源模式
RCC配置内部时钟
GPIO口ETR外部时钟模式2
触发输入外部时钟模式1(可选时钟源:ETR外部时钟，ITRx其他定时器，TIx捕获通道)
编码器模式GPIO口TIx捕获通道

定时器内的时基单元寄存器
PSC预分频器
CNT计数器
ARR自动重装器

配置时钟产生并发送到外设(RCC时钟树)
SystemInit()函数
首先系统时钟72MHz(8MHZx9)进入AHB总线,里面有预分频器,在SystemInit里配置1就是72MHz
然后进入APB1总线,在SystemInit函数里预分频器配置为2,该总线时钟为36MHz,通向APB1外设
但是通向定时器2-7时,如果APB1预分频器不为1,那么频率需要x2,重现变成72MHz
APB2同理
*/
#include "stm32f10x.h"
//每隔1ms触发中断函数进入TIM2_IRQHandler()
void Timer_Init(void)
{
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM2,ENABLE);              //  开启RCC时钟
	TIM_InternalClockConfig(TIM2);                                   //  选择内部时钟和中断通道
	
	TIM_TimeBaseInitTypeDef TIM_TimeBaseInitStructure;               //  配置时基单元
	TIM_TimeBaseInitStructure.TIM_ClockDivision=TIM_CKD_DIV1;        //  设置时钟分频
	TIM_TimeBaseInitStructure.TIM_CounterMode=TIM_CounterMode_Up;    //  配置向下计数
    /*定时器溢出频率=CK_CNT/(APP+1)=CK_PSC[72MHz]/(PSC+1)/(ARR+1)   10ms=100Hz */
	TIM_TimeBaseInitStructure.TIM_Period=100-1;                    	 //  ARR自动重装器的值(0~65535)
	TIM_TimeBaseInitStructure.TIM_Prescaler=7200-1;                  //  PSC预分频器的值(0~65535)
	TIM_TimeBaseInitStructure.TIM_RepetitionCounter=0;               //  重复寄存器的值(高级定时器才有)
	TIM_TimeBaseInit(TIM2,&TIM_TimeBaseInitStructure);
	
	/*TIM_TimeBaseInit函数会自动生成更新事件加载预分频器的值和自动重装器的值，导致一初始化即刚上电就会进入一次中断，然后定时器从1开始计时*/
	TIM_ClearFlag(TIM2,TIM_FLAG_Update);                             //  所以这里我们要清除一下标志位
	
	TIM_ITConfig(TIM2,TIM_IT_Update,ENABLE);                         //  使能中断信号更新输出到NVIC
	NVIC_InitTypeDef NVIC_InitStructure;
	NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);				 //  已经在串口里配置好分组
	NVIC_InitStructure.NVIC_IRQChannel=TIM2_IRQn;                    //  选择中断通道
	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority=3;//2          //  抢占优先级
	NVIC_InitStructure.NVIC_IRQChannelSubPriority=0;                 //  响应优先级
	NVIC_InitStructure.NVIC_IRQChannelCmd=ENABLE;
	NVIC_Init(&NVIC_InitStructure);
	
	TIM_Cmd(TIM2,ENABLE);
}


//void TIM2_IRQHandler(void)       //  中断函数具有固定格式,在启动文件内获取
//{
//	
//}

/*定时器库函数注释

# 恢复初始配置(清零)
void TIM_DeInit(TIM_TypeDef* TIMx);

# 时基单元初始化函数,第一个参数选择配置的定时器,第二个结构体包含了配置时基单元的参数
void TIM_TimeBaseInit(TIM_TypeDef* TIMx, TIM_TimeBaseInitTypeDef* TIM_TimeBaseInitStruct);

# 把结构体变量赋一个默认值,参数为指定的结构体地址
void TIM_TimeBaseStructInit(TIM_TimeBaseInitTypeDef* TIM_TimeBaseInitStruct);

# 使能计数器(使能控制),第一个选择使能的计数器,第二个参数是选择使能或者不使能ENABLE or DISABLE
void TIM_Cmd(TIM_TypeDef* TIMx, FunctionalState NewState);

# 使能中断输出信号 中断输出控制,第一个选择定时器，第二个参数选择要配置哪个中断输出，第三个选择使能或者失能
void TIM_ITConfig(TIM_TypeDef* TIMx, uint16_t TIM_IT, FunctionalState NewState);

/时基单元的时钟源选择

# 选择内部时钟
void TIM_InternalClockConfig(TIM_TypeDef* TIMx);
# 选择ITRx其他定时器的时钟，第二个参数选择要接入哪个其他的定时器
void TIM_ITRxExternalClockConfig(TIM_TypeDef* TIMx, uint16_t TIM_InputTriggerSource);
# 选择TIx捕获通道的时钟，第二个选择TIx具体的引脚，第三个参数选择输入的极性IM_ICPolarity_Rising/TIM_ICPolarity_Falling，第四个滤波器
void TIM_TIxExternalClockConfig(TIM_TypeDef* TIMx, uint16_t TIM_TIxExternalCLKSource,
                                uint16_t TIM_ICPolarity, uint16_t ICFilter);
# 选择ETR通过外部时钟模式1输入的时钟，第一个参数外部触发预分频器，对ETR外部时钟提前做一个预分频，后面两个是输入极性和滤波器								
void TIM_ETRClockMode1Config(TIM_TypeDef* TIMx, uint16_t TIM_ExtTRGPrescaler, uint16_t TIM_ExtTRGPolarity,
                             uint16_t ExtTRGFilter);
# 选择ETR通过外部时钟模式2输入的时钟
void TIM_ETRClockMode2Config(TIM_TypeDef* TIMx, uint16_t TIM_ExtTRGPrescaler, 
                             uint16_t TIM_ExtTRGPolarity, uint16_t ExtTRGFilter);
# 配置ETR引脚的预分频器，极性，滤波器参数
void TIM_ETRConfig(TIM_TypeDef* TIMx, uint16_t TIM_ExtTRGPrescaler, uint16_t TIM_ExtTRGPolarity,
                   uint16_t ExtTRGFilter);
//
# 初始化后可能会更改预分频值，这个函数就是单独写预分频值，第二个就是要写的预分频值，第三个选择写入模式
void TIM_PrescalerConfig(TIM_TypeDef* TIMx, uint16_t Prescaler, uint16_t TIM_PSCReloadMode);

# 改变计数器模式，第二个参数就是选择新的计数器模式
void TIM_CounterModeConfig(TIM_TypeDef* TIMx, uint16_t TIM_CounterMode);

# 自动重装器预装功能配置,有预装的话当自动加载寄存器的值更新依然会等到计数器溢出中断更新后再改变值
void TIM_ARRPreloadConfig(TIM_TypeDef* TIMx, FunctionalState NewState);

# 给计数器写入一个值
void TIM_SetCounter(TIM_TypeDef* TIMx, uint16_t Counter);

# 给自动重装器写一个值
void TIM_SetAutoreload(TIM_TypeDef* TIMx, uint16_t Autoreload);

# 获取当前计数器的值
uint16_t TIM_GetCounter(TIM_TypeDef* TIMx);
# 获取当前预分频器的值
uint16_t TIM_GetPrescaler(TIM_TypeDef* TIMx);
# 主程序中获取指定计数器的标志位
FlagStatus TIM_GetFlagStatus(TIM_TypeDef* TIMx, uint16_t TIM_FLAG);
# 主程序清除标志位
void TIM_ClearFlag(TIM_TypeDef* TIMx, uint16_t TIM_FLAG);
# 中段程序中获取标志位
ITStatus TIM_GetITStatus(TIM_TypeDef* TIMx, uint16_t TIM_IT);
# 中断程序中清除标志位
void TIM_ClearITPendingBit(TIM_TypeDef* TIMx, uint16_t TIM_IT);

*/

