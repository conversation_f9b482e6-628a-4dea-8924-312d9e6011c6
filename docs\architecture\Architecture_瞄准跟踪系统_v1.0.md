# 瞄准跟踪系统架构设计文档

## 1. 文档信息
- **版本**: v1.0
- **创建日期**: 2025-08-02
- **负责人**: Bob (架构师)
- **项目名称**: 瞄准模块连续跟踪系统架构

## 2. 架构概述

### 2.1 设计原则
- **最小侵入**: 完全复用现有`Target_practice()`框架，不修改原有代码
- **状态驱动**: 基于状态机实现连续跟踪逻辑
- **实时响应**: 10ms周期调用，确保实时性
- **异常安全**: 完善的异常检测和恢复机制

### 2.2 核心架构
```
┌─────────────────────────────────────────────────────────┐
│                瞄准跟踪系统架构                          │
├─────────────────────────────────────────────────────────┤
│  用户接口层    │ Target_Tracking_Start/Stop/Reset      │
├─────────────────────────────────────────────────────────┤
│  状态机层      │ TRACK_IDLE → TRACK_SEARCHING →        │
│                │ TRACK_FOLLOWING (循环)                │
├─────────────────────────────────────────────────────────┤
│  控制算法层    │ 连续PID控制 + 激光管理 + 异常处理     │
├─────────────────────────────────────────────────────────┤
│  硬件抽象层    │ 复用现有PID/电机/激光/显示接口        │
└─────────────────────────────────────────────────────────┘
```

## 3. 状态机设计

### 3.1 状态定义
```c
typedef enum {
    TRACK_IDLE = 0,        // 空闲状态
    TRACK_SEARCHING = 1,   // 搜索目标状态  
    TRACK_FOLLOWING = 2    // 连续跟踪状态
} TrackingState_t;
```

### 3.2 状态转换图
```
    [启动跟踪]
TRACK_IDLE ────────────→ TRACK_SEARCHING
    ↑                           │
    │                           │ [检测到目标]
    │                           ↓
    │                   TRACK_FOLLOWING
    │                           │ ↑
    │                           │ │ [持续跟踪]
    │                           ↓ │
    └─────────── [停止跟踪] ─────┘
```

### 3.3 状态行为定义

#### TRACK_IDLE (空闲状态)
- **进入条件**: 系统启动或调用停止函数
- **执行动作**: 关闭激光，停止电机，清除状态
- **退出条件**: 调用`Target_Tracking_Start()`

#### TRACK_SEARCHING (搜索状态)
- **进入条件**: 从空闲状态启动或目标丢失
- **执行动作**: 开启激光，等待目标出现，显示搜索状态
- **退出条件**: `light.target_flag==1` 或调用停止函数

#### TRACK_FOLLOWING (跟踪状态)
- **进入条件**: 检测到目标点
- **执行动作**: 连续PID控制，激光保持开启，实时跟踪
- **退出条件**: 目标长时间丢失或调用停止函数

## 4. 算法设计

### 4.1 连续跟踪算法流程
```
┌─────────────────┐
│  获取激光数据    │
└─────────┬───────┘
          │
          ↓
┌─────────────────┐    NO    ┌─────────────────┐
│  数据有效检查    │ ────────→ │  使用上次数据    │
└─────────┬───────┘          └─────────────────┘
          │ YES
          ↓
┌─────────────────┐
│  状态机处理      │
└─────────┬───────┘
          │
          ↓
┌─────────────────┐
│  PID控制计算     │
└─────────┬───────┘
          │
          ↓
┌─────────────────┐
│  激光状态管理    │
└─────────┬───────┘
          │
          ↓
┌─────────────────┐
│  显示状态更新    │
└─────────────────┘
```

### 4.2 PID控制策略

#### 4.2.1 连续控制模式
- **目标更新**: 实时更新目标坐标为`light.target_x/y`
- **位置反馈**: 使用`light.purple_x/y`作为当前位置
- **控制输出**: 直接调用`app_pid_calc()`执行控制

#### 4.2.2 目标丢失处理
```c
// 目标丢失计数器
static uint32_t target_lost_timer = 0;

if(light.target_flag == 0) {
    target_lost_timer++;
    if(target_lost_timer > TARGET_LOST_THRESHOLD) {
        // 切换到搜索状态
        tracking_state = TRACK_SEARCHING;
        target_lost_timer = 0;
    }
    // 保持最后已知位置
    return;
} else {
    target_lost_timer = 0; // 重置计数器
}
```

### 4.3 激光管理算法

#### 4.3.1 激光状态控制
```c
typedef struct {
    uint8_t laser_enabled;      // 激光使能标志
    uint32_t laser_check_timer; // 激光检查计时器
    uint8_t laser_error_count;  // 激光错误计数
} LaserManager_t;
```

#### 4.3.2 激光保活机制
- **连续发光**: 跟踪状态下激光始终开启
- **状态检查**: 每100ms检查一次激光状态
- **自动恢复**: 检测到激光关闭时自动重新开启

## 5. 数据结构设计

### 5.1 跟踪状态数据
```c
typedef struct {
    TrackingState_t current_state;  // 当前状态
    uint8_t is_tracking;           // 跟踪标志
    uint8_t target_detected;       // 目标检测标志
    uint32_t state_timer;          // 状态计时器
    uint32_t target_lost_timer;    // 目标丢失计时器
    
    // 最后已知的有效坐标
    float last_valid_target_x;
    float last_valid_target_y;
    float last_valid_purple_x;
    float last_valid_purple_y;
    
    // 激光管理
    LaserManager_t laser_manager;
    
    // 统计信息
    uint32_t total_tracking_time;  // 总跟踪时间
    uint32_t target_lost_count;    // 目标丢失次数
} TrackingStatus_t;
```

### 5.2 配置参数
```c
typedef struct {
    uint32_t target_lost_threshold;    // 目标丢失阈值 (ms)
    uint32_t laser_check_interval;     // 激光检查间隔 (ms)
    uint8_t max_laser_error_count;     // 最大激光错误次数
    uint8_t enable_status_display;     // 是否显示状态信息
} TrackingConfig_t;
```

## 6. 接口设计

### 6.1 主要函数接口
```c
// 主跟踪函数 (10ms周期调用)
void Target_Tracking(void);

// 控制接口
void Target_Tracking_Start(void);    // 启动跟踪
void Target_Tracking_Stop(void);     // 停止跟踪  
void Target_Tracking_Reset(void);    // 重置状态

// 状态查询接口
uint8_t Target_Is_Tracking(void);    // 是否正在跟踪
uint8_t Target_Has_Target(void);     // 是否检测到目标
TrackingState_t Target_Get_State(void); // 获取当前状态

// 配置接口
void Target_Set_Config(TrackingConfig_t* config);
TrackingStatus_t* Target_Get_Status(void);
```

### 6.2 复用现有接口映射
```c
// 数据获取接口
Get_light_data(&light) → 获取激光和目标坐标

// PID控制接口  
app_pid_set_target(x, y) → 设置PID目标
app_pid_update_position(x, y) → 更新当前位置
app_pid_calc() → 执行PID控制

// 激光控制接口
JG_Ton() → 开启激光
JG_Toff() → 关闭激光

// 显示接口
OLED_ShowString() → 显示状态信息
OLED_Update() → 更新显示
```

## 7. 异常处理设计

### 7.1 异常类型定义
```c
typedef enum {
    TRACK_ERROR_NONE = 0,
    TRACK_ERROR_DATA_INVALID = 1,    // 数据无效
    TRACK_ERROR_TARGET_LOST = 2,     // 目标丢失
    TRACK_ERROR_LASER_FAIL = 3,      // 激光故障
    TRACK_ERROR_MOTOR_FAIL = 4,      // 电机故障
    TRACK_ERROR_COMM_TIMEOUT = 5     // 通信超时
} TrackingError_t;
```

### 7.2 异常处理策略

#### 7.2.1 数据异常处理
- **坐标越界**: 忽略无效数据，使用上次有效坐标
- **数据缺失**: 保持当前状态，等待有效数据
- **通信中断**: 切换到搜索状态，停止PID控制

#### 7.2.2 硬件异常处理
- **激光异常**: 记录错误，尝试重新开启，超过阈值则报警
- **电机异常**: 立即停止跟踪，切换到空闲状态
- **系统异常**: 执行安全停止，关闭所有输出

### 7.3 异常恢复机制
```c
static void Handle_Tracking_Error(TrackingError_t error) {
    switch(error) {
        case TRACK_ERROR_TARGET_LOST:
            // 切换到搜索状态，保持激光开启
            tracking_state = TRACK_SEARCHING;
            break;
            
        case TRACK_ERROR_LASER_FAIL:
            // 尝试重新开启激光
            laser_manager.laser_error_count++;
            if(laser_manager.laser_error_count < MAX_LASER_ERROR_COUNT) {
                JG_Ton();
            } else {
                // 激光故障，停止跟踪
                Target_Tracking_Stop();
            }
            break;
            
        case TRACK_ERROR_MOTOR_FAIL:
            // 电机故障，立即停止
            Target_Tracking_Stop();
            break;
            
        default:
            break;
    }
}
```

## 8. 性能优化设计

### 8.1 计算优化
- **条件判断优化**: 使用快速路径减少不必要的计算
- **数据缓存**: 缓存上次有效数据，避免重复计算
- **状态机优化**: 最小化状态转换开销

### 8.2 内存优化
- **静态分配**: 所有数据结构使用静态分配，避免动态内存
- **数据复用**: 复用现有PID控制器的内存空间
- **缓冲区管理**: 合理管理显示缓冲区

### 8.3 实时性保证
- **中断优先级**: 确保10ms定时器中断优先级合适
- **执行时间控制**: 单次执行时间控制在1ms以内
- **任务分片**: 将复杂计算分片执行

## 9. 集成方案

### 9.1 与现有系统集成
```c
// 在main.c的定时器中断中添加
void TIM2_IRQHandler(void) {
    if(TIM_GetITStatus(TIM2,TIM_IT_Update)==SET) {
        TIM_ClearITPendingBit(TIM2, TIM_IT_Update);
        key_Loop();
        
        switch (Mode) {
            case 1: Target_practice(); break;
            case 2: /* 持续打靶 */ break;
            case 3: /* 轨迹循环 */ break;
            case 4: Target_Tracking(); break; // 新增跟踪模式
            default: break;
        }
    }
}
```

### 9.2 按键控制集成
```c
// 在main.c主循环中添加
if(KeyNum == 6) { // 新增按键6用于跟踪模式
    // 启动跟踪模式
    Motor_Enable(Usart_up, 1, true, false);
    Motor_Enable(Usart_below, 1, true, false);
    app_pid_init();
    Target_Tracking_Start();
    Mode = 4; // 跟踪模式
    
    // 等待退出
    while(flag) {
        uint8_t temp = Key_GetNum();
        if(temp == 6) {
            Target_Tracking_Stop();
            Mode = 0;
            flag = 0;
        }
    }
}
```

## 10. 测试验证方案

### 10.1 单元测试
- **状态机测试**: 验证状态转换逻辑正确性
- **算法测试**: 验证PID控制和激光管理算法
- **异常测试**: 验证各种异常情况的处理

### 10.2 集成测试
- **硬件集成**: 验证与电机、激光器的接口
- **系统集成**: 验证与现有系统的兼容性
- **性能测试**: 验证实时性和稳定性

### 10.3 验收测试
- **功能验收**: 验证连续跟踪功能完整性
- **性能验收**: 验证跟踪精度和响应速度
- **稳定性验收**: 长时间运行稳定性测试

## 11. 部署指南

### 11.1 文件组织
```
Hardware/
├── Target_Tracking.h    // 跟踪系统头文件
├── Target_Tracking.c    // 跟踪系统实现
└── Head.h              // 添加包含关系

User/
└── main.c              // 添加集成代码

docs/
├── architecture/       // 架构文档
├── development/        // 开发文档
└── examples/          // 使用示例
```

### 11.2 编译配置
- 确保所有依赖的头文件正确包含
- 验证编译器优化设置
- 检查内存和Flash使用情况

### 11.3 调试配置
- 配置串口调试输出
- 设置OLED显示调试信息
- 配置性能监控点

## 12. 维护和扩展

### 12.1 代码维护
- 保持代码注释的完整性
- 定期更新文档
- 版本控制和变更记录

### 12.2 功能扩展
- 支持多目标跟踪
- 增加预测算法
- 优化跟踪精度

### 12.3 性能优化
- 算法优化
- 硬件升级适配
- 实时性提升
