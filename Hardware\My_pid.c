#include "Head.H"


// 打靶状态枚举
typedef enum {
    TARGET_SEARCHING = 0,  // 搜索目标状态
    TARGET_AIMING = 1,     // 瞄准状态
    TARGET_FIRING = 2,     // 开火状态
    TARGET_COMPLETE = 3    // 完成状态
} TargetState_t;

static TargetState_t target_state = TARGET_SEARCHING;
static uint32_t fire_timer = 0;        // 激光开启计时器
static uint8_t target_reached = 0;     // 目标到达标志
static uint32_t status_display_timer = 0; // 状态显示计时器

void Target_practice(void)
{
    light_position light;
    // 获取激光数据
    if(Get_light_data(&light))
    {
        switch(target_state)
        {
            case TARGET_SEARCHING:
                // 搜索状态：检测是否有目标点
                if(light.target_flag==1)
                {
                    // 检测到目标点，停止X轴旋转
                    Stop_Now(Usart_below,1,false);
                    // 切换到瞄准状态
                    target_state=TARGET_AIMING;
                    target_reached=0;
                }
                break;

            case TARGET_AIMING:
                // 瞄准状态：PID控制使紫色激光对准目标点
                if(light.purple_flag==1&&light.target_flag==1)
                {
                    // 设置目标坐标为目标点坐标
                    app_pid_set_target(light.target_x, light.target_y);
                    // 更新当前坐标为紫色激光坐标
                    app_pid_update_position(light.purple_x, light.purple_y);
                    // 执行PID控制
                    app_pid_calc();
                    // 检查是否到达目标点（在死区内）
                    int error_x=light.target_x-light.purple_x;
                    int error_y=light.target_y-light.purple_y;
					OLED_ShowString(0,16,"OK",OLED_8X16);
                    if(abs(error_x)<=3&&abs(error_y)<=3) // 死区范围3像素
                    {
						OLED_ShowString(0,32,"Find_OK",OLED_8X16);
						
                        target_reached = 1;
                        // 停止电机运动
                        Stop_Now(Usart_below, 1, false);
                        Stop_Now(Usart_up, 1, false);
                        // 切换到开火状态
                        target_state=TARGET_FIRING;
                        fire_timer=0; // 重置计时器
                        // 立即开启激光
                        JG_Ton();
                    }
					OLED_Update();
                }
                break;
            case TARGET_FIRING:
                // 开火状态：激光开启一段时间后关闭
                fire_timer++;
                if(fire_timer >= 100) // 100 * 10ms = 1秒
                {
                    // 关闭激光
                    JG_Toff();
                    // 切换到完成状态
                    target_state = TARGET_COMPLETE;
                }
                break;
            case TARGET_COMPLETE:
                // 完成状态：等待用户操作
                // 可以在这里添加完成后的处理逻辑
                break;
            default:
                target_state = TARGET_SEARCHING;
                break;
        }
    }
}

// 打靶状态重置函数
void Target_practice_reset(void)
{
    target_state = TARGET_SEARCHING;
    fire_timer = 0;
    target_reached = 0;
    status_display_timer = 0;
    // 确保激光关闭
    JG_Toff();
}

PID_T pid_x; // X轴PID控制器
PID_T pid_y; // Y轴PID控制器

PidParams_t pid_params_x = {
    .kp = 0.2f,   // 增大Kp提高响应速度
    .ki = 0.0, // 增加积分项减小稳态误差
    .kd = 0.0, // 增加微分项提高稳定性
    .out_min = -99.0f,
    .out_max = 99.0f,
    .i_min = -80.0f,
    .i_max = 80.0f,
    .deadzone = 3 // 减小死区大小，提高精度
};

PidParams_t pid_params_y = {
    .kp = 0.5f,   // 增大Kp提高响应速度
    .ki = 0.0, // 增加积分项减小稳态误差
    .kd = 0.0, // 增加微分项提高稳定性
    .out_min = -99.0f,
    .out_max = 99.0f,
    .i_min = -80.0f,
    .i_max = 80.0f,
    .deadzone = 3 // 减小死区大小，提高精度
};

// PID目标坐标
int target_x = 320; // 默认屏幕中心
int target_y = 240; // 默认屏幕中心

// 当前实际坐标
int current_x = 320;
int current_y = 240;

// 电机输出值
int8_t motor_x, motor_y;

// 快速饱和处理宏
#define CONSTRAIN(x, min, max) ((x) < (min) ? (min) : ((x) > (max) ? (max) : (x)))

//PID限幅功能函数
static void app_pid_limit_integral(PID_T *pid, float min, float max)
{
    if (pid->integral > max)
    {
        pid->integral = max;
    }
    else if (pid->integral < min)
    {
        pid->integral = min;
    }
}

//PID初始化
void app_pid_init(void)
{
    // 初始化X轴PID控制器
    pid_init(&pid_x,
             pid_params_x.kp, pid_params_x.ki, pid_params_x.kd,
             (float)current_x, pid_params_x.out_max);
    // 初始化Y轴PID控制器
    pid_init(&pid_y,
             pid_params_y.kp, pid_params_y.ki, pid_params_y.kd,
             (float)current_y, pid_params_y.out_max);
}

//设置PID目标坐标(目标点
void app_pid_set_target(int x, int y)
{
    target_x = x;
    target_y = y;
    // 使用我们的PID中间件设置目标值
    pid_set_target(&pid_x, (float)target_x);
    pid_set_target(&pid_y, (float)target_y);
}

//更新当前坐标(紫色虚拟点
void app_pid_update_position(int x, int y)
{
    // 检查数据有效性
    if(x<0||x>320||y<0||y>240)
    {
        return; // 无效数据直接丢弃
    }
    current_x = x;
    current_y = y;
}

//PID控制电机输出
void app_pid_calc(void)
{
    int error_x, error_y;
    float output_x, output_y;
    // 计算X轴误差
    error_x=target_x-current_x;
    error_y=target_y-current_y;
    // 只有同时在死区内才停止电机
    if(abs(error_x)<=pid_params_x.deadzone && abs(error_y) <= pid_params_y.deadzone)
    {
        Stop_Now(Usart_below,1,false);
		Stop_Now(Usart_up,1,false);
        return;
    }
    // 使用位置式PID计算X轴输出
    output_x=pid_calculate_positional(&pid_x,(float)current_x);
    // 执行积分限幅
    app_pid_limit_integral(&pid_x, pid_params_x.i_min, pid_params_x.i_max);
    // 使用位置式PID计算Y轴输出
    output_y = pid_calculate_positional(&pid_y, (float)current_y);
    // 执行积分限幅
    app_pid_limit_integral(&pid_y, pid_params_y.i_min, pid_params_y.i_max);
    // 限幅处理
    output_x=CONSTRAIN(output_x, pid_params_x.out_min, pid_params_x.out_max);
    output_y=CONSTRAIN(output_y, pid_params_y.out_min, pid_params_y.out_max);
    // 获取最终电机控制值
    motor_x=(int8_t)output_x;
    motor_y=(int8_t)output_y;
    // 控制电机
    //Motor_Set_Speed(-motor_x, -motor_y);
    // 高精度控制
    Step_Motor_Set_Speed_my(-motor_x, -motor_y);
}
