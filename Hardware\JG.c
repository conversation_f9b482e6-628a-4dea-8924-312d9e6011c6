#include "stm32f10x.h"                  // Device header
void JG_Init(void)
{
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA,ENABLE); // 关键修复：使能GPIOA时钟
	GPIO_InitTypeDef GPIO_InitStructure;
	GPIO_InitStructure.GPIO_Pin=GPIO_Pin_11;
	GPIO_InitStructure.GPIO_Mode=GPIO_Mode_Out_PP;   //推挽输出
	GPIO_InitStructure.GPIO_Speed=GPIO_Speed_50MHz;
	GPIO_Init(GPIOA,&GPIO_InitStructure);

	GPIO_ResetBits(GPIOA,GPIO_Pin_11);
}

void JG_Toff(void)  // 关闭激光
{
	GPIO_ResetBits(GPIOA,GPIO_Pin_11);
}

void JG_Ton(void)   // 开启激光
{
	GPIO_SetBits(GPIOA,GPIO_Pin_11);
}
