#include "Head.H"
uint32_t Pulse;			// 脉冲,根据实际需要将error*3/2映射到Pulse上
uint8_t add=1;			// 电机地址
uint8_t dir;			// 转向CW0顺时针	CCW1逆时针
uint16_t Speed=30;		// 设置电机速度
uint8_t acc=10;			// 设置电机加速度

int main(void)
{
	OLED_Init();
    KEY_Init();
	JG_Init();
	Init_Queue();
	Init_Queue_3();			//初始化串口3 FIFO队列
	DoubleBuffer_Init();
	Serial_Init();			//在电机初始化之前调用
	My_motor_Init();		//上电不使能
	Timer_Init();
	//Menu_Init();
	//MenuRuning(head);
	
	
	while(1)
	{
		uint8_t KeyNum=Key_GetNum();
		OLED_Printf(0,0,OLED_8X16,"未启动");
		// 固定点位标定参数
		if(KeyNum==1)
		{
			OLED_Clear();
			OLED_ShowString(0,0,"Wait_BD..",OLED_8X16);
			OLED_Update();
			JG_Ton();  // 开启激光（高电平）
			uint8_t flag=1;
			while(flag)
			{
				uint8_t temp=Key_GetNum();
				if(temp==1)
				{
					Motor_Enable(Usart_up,1,true,false);
					Motor_Enable(Usart_below,1,true,false);
					Delay_ms(5);
					Set_zero_position(Usart_up,1,true);
					Set_zero_position(Usart_below,1,true);
					OLED_ShowString(0,0,"BD_OK..",OLED_8X16);
					Delay_ms(5);
					JG_Toff();
					Motor_Enable(Usart_up,1,false,false);
					Motor_Enable(Usart_below,1,false,false);
					flag=0;
					temp=0;
				}
				OLED_Update();
			}
		}
		//复位功能(任务2)
		if(KeyNum==2)
		{
			uint8_t flag=0;
			OLED_Clear();
			OLED_Printf(0,0,OLED_8X16,"启动");
			OLED_ShowString(0,16,"Return..",OLED_8X16);
			Motor_Enable(Usart_up,1,true,false);
			Motor_Enable(Usart_below,1,true,false);
			Delay_ms(5);
			Return_zero(Usart_up,1,0,false);
			Return_zero(Usart_below,1,0,false);
			Delay_ms(5);
			// 检查串口2和串口3的接收状态
			if(Serial_GetRxflag_2()==true && Serial_GetRxflag_3()==true)
			{
				Delay_ms(500);
				JG_Ton();
				OLED_ShowString(0,16,"Finish..",OLED_8X16);
				Delay_ms(1000);
				JG_Toff();
				OLED_Clear();
				flag=1;
			}
			//再按下按键2退出任务2
			while(flag)
			{
				uint8_t temp=Key_GetNum();
				if(temp==2)
				{
					Motor_Enable(Usart_up,1,false,false);
					Motor_Enable(Usart_below,1,false,false);
					flag=0;
					temp=0;
				}
				OLED_Update();
			}
		}
		//打靶功能(任务3)
		//按下按键3，Y轴电机回归零点，X轴电机开始旋转，检测到目标点后停止旋转，进行PID控制打靶
		if(KeyNum==3)
		{
			uint8_t flag=1;
			OLED_Clear();
			OLED_Printf(0,0,OLED_8X16,"打靶模式");
			OLED_Update();
			Motor_Enable(Usart_up,1,true,false);
			Motor_Enable(Usart_below,1,true,false);
			Delay_ms(5);
			Return_zero(Usart_up,1,0,false);
			app_pid_init();
			//速度是逆时针
			V_Control(Usart_below,1,1,150,0,false);
			Mode=1;
			//再按下按键3退出任务3
			while(flag)
			{
				uint8_t temp=Key_GetNum();
				if(temp==3)
				{
					Motor_Enable(Usart_up,1,false,false);
					Motor_Enable(Usart_below,1,false,false);
					flag=0;
					temp=0;
					Mode=0;
					JG_Toff();
				}
			}
			
		}

		// 持续打靶
		if(KeyNum==4)
		{
			uint8_t flag=1;
			OLED_Clear();
			OLED_Printf(0,0,OLED_8X16,"持续打靶");
			OLED_Update();
			Motor_Enable(Usart_up,1,true,false);
			Motor_Enable(Usart_below,1,true,false);
			Delay_ms(5);
			Return_zero(Usart_up,1,0,false);
			Return_zero(Usart_below,1,0,false);
			Delay_ms(500);	
			app_pid_init();
			Mode=2;
			JG_Ton();
			//再按下按键4退出发挥1
			while(flag)
			{
				uint8_t temp=Key_GetNum();
				if(temp==4)
				{
					Motor_Enable(Usart_up,1,false,false);
					Motor_Enable(Usart_below,1,false,false);
					flag=0;
					temp=0;
					Mode=0;
				}
			}
		}
		// 绘制圆圈/摄像头那边选择传递圆形数据
		if(KeyNum==5)
		{
			uint8_t flag=1;
			OLED_Clear();
			OLED_Printf(0,0,OLED_8X16,"持续打靶");
			OLED_Update();
			Motor_Enable(Usart_up,1,true,false);
			Motor_Enable(Usart_below,1,true,false);
			Delay_ms(5);
			Return_zero(Usart_up,1,0,false);
			Return_zero(Usart_below,1,0,false);
			Delay_ms(500);	
			app_pid_init();
			Mode=2;
			JG_Ton();
			//再按下按键4退出发挥1
			while(flag)
			{
				uint8_t temp=Key_GetNum();
				if(temp==4)
				{
					Motor_Enable(Usart_up,1,false,false);
					Motor_Enable(Usart_below,1,false,false);
					flag=0;
					temp=0;
				}
			}
		}

		OLED_Update();
	}
}

// 定时器中断服务函数 (10ms周期)
void TIM2_IRQHandler(void)
{
  // 步进电机控制中断
  if(TIM_GetITStatus(TIM2,TIM_IT_Update)==SET)
  {
	TIM_ClearITPendingBit(TIM2, TIM_IT_Update);
	//按键相关
	key_Loop();
	switch (Mode)
	{
		case 1:	// 任务3
			Target_practice();
			break;
		case 2: // 拓展1
			break;
		case 3: // 拓展2
			break;
		default:
			break;
	}
  }
}
 

