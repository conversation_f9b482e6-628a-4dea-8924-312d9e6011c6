#include "stm32f10x.h"                  // Device header
#include "Serial.H"
#include "Stepping_motor.H"
#include "Delay.H"

//README
//发送接收格式：地址+功能码+指令数据+校验字节
//地址：默认为 1，可设置范围为 1-255，0 为广播地址
//校验：默认每条命令的最后一个字节都是0x6B作为校验
//返回值:  电机地址+功能马+02/E2/EE+6B（02命令正确/E2条件不满/EE错误命令+）
//关于多机：如果两个电机共用一个串口，需要设置多机控制；各用一个那就没事了
/*
1.8°电机，32细分->360°（一圈）=6400脉冲 （360/1.8*32=200*32,17.77个脉冲等于1°）脉冲频率越快速度越快
		  16细分->一圈=3200脉冲（360/1.8*16=200*16,8.88个脉冲等于1°）
0.9°电机...
1RPM(转/分)=6°每秒
*/
//上电不使能
void My_motor_Init(void)
{
	Motor_Enable(Usart_up,1,false,false);
	Motor_Enable(Usart_below,1,false,false);
}

/**
 * @brief 设置XY轴电机速度
 * @param x_percent X轴速度百分比，范围-100到100
 * @param y_percent Y轴速度百分比，范围-100到100
 */
void Motor_Set_Speed(int8_t x_percent, int8_t y_percent)
{
    uint8_t x_dir, y_dir;
    uint16_t x_speed, y_speed;
    /* 限制百分比范围 */
    if (x_percent > 100)
        x_percent = 100;
    if (x_percent < -100)
        x_percent = -100;
    if (y_percent > 100)
        y_percent = 100;
    if (y_percent < -100)
        y_percent = -100;
    /* 设置X轴方向 */
    if (x_percent >= 0)
    {
        x_dir = 0; /* CW方向 */
    }
    else
    {
        x_dir=1;              /* CCW方向 */
        x_percent=-x_percent; /* 取绝对值 */
    }
    /* 设置Y轴方向 */
    if (y_percent>=0)
    {
        y_dir = 0; /* CW方向 */
    }
    else
    {
        y_dir = 1;              /* CCW方向 */
        y_percent=-y_percent; /* 取绝对值 */
    }
    /* 计算实际速度值(百分比转换为RPM) */
    x_speed = (uint16_t)((x_percent * MOTOR_MAX_SPEED) / 100);
    y_speed = (uint16_t)((y_percent * MOTOR_MAX_SPEED) / 100);
    /* 控制X轴电机 */
	  V_Control(Usart_below,1,x_dir,x_speed,0,false);
    /* 控制Y轴电机 */
	  V_Control(Usart_up,1,y_dir,y_speed,0,false);
}

/**
 * @brief 设置XY轴电机速度
 * @param x_rpm X轴目标速度，单位RPM (revolutions per minute)。
 *              支持负值表示反向。
 *              速度精度为0.1RPM。小于0.05 RPM的绝对值将被量化为0。
 * @param y_rpm Y轴目标速度，单位RPM (revolutions per minute)。
 *              支持负值表示反向。
 *              速度精度为0.1RPM。小于0.05 RPM的绝对值将被量化为0。
 */
//最大速度需要扩大十倍
void Step_Motor_Set_Speed_my(float x_rpm, float y_rpm)
{
    uint8_t x_dir, y_dir;
    uint16_t x_speed_scaled, y_speed_scaled; // 速度值，单位为 0.1 RPM
    float abs_x_rpm, abs_y_rpm;

    /* 1. 限制输入RPM范围，确保不超过电机最大物理速度 */
    // 将输入的RPM值钳位在 [-MOTOR_MAX_SPEED, MOTOR_MAX_SPEED] 之间
    if (x_rpm > MOTOR_MAX_SPEED)
    {
        x_rpm = MOTOR_MAX_SPEED;
    }
    else if (x_rpm < -MOTOR_MAX_SPEED)
    {
        x_rpm = -MOTOR_MAX_SPEED;
    }

    if (y_rpm > MOTOR_MAX_SPEED)
    {
        y_rpm = MOTOR_MAX_SPEED;
    }
    else if (y_rpm < -MOTOR_MAX_SPEED)
    {
        y_rpm = -MOTOR_MAX_SPEED;
    }

    /* 2. 处理X轴方向和获取绝对速度 */
    if (x_rpm >= 0.0f)
    {
        x_dir = 0; /* CW方向 (正转) */
        abs_x_rpm = x_rpm;
    }
    else
    {
        x_dir = 1; /* CCW方向 (反转) */
        abs_x_rpm = -x_rpm; /* 取绝对值 */
    }

    /* 3. 处理Y轴方向和获取绝对速度 */
    if (y_rpm >= 0.0f)
    {
        y_dir = 0; /* CW方向 (正转) */
        abs_y_rpm = y_rpm;
    }
    else
    {
        y_dir = 1; /* CCW方向 (反转) */
        abs_y_rpm = -y_rpm; /* 取绝对值 */
    }

    /* 4. 计算实际发送给电机控制器的速度值 (单位为 0.1 RPM) */
    // 将RPM值乘以10，得到以0.1RPM为单位的整数值。
    // 加上0.5f是为了进行四舍五入。
    // 这样，例如 0.04 RPM (0.4 scaled) + 0.5f = 0.9f -> 0 (uint16_t)
    // 0.05 RPM (0.5 scaled) + 0.5f = 1.0f -> 1 (uint16_t)
    x_speed_scaled = (uint16_t)(abs_x_rpm * 10 + 0.5f);
    y_speed_scaled = (uint16_t)(abs_y_rpm * 10 + 0.5f);
    
    // 再次检查计算出的 scaled speed 是否超出 uint16_t 的最大值，
    // 理论上在输入钳位后 (MOTOR_MAX_SPEED * RPM_TO_SCALED_FACTOR) 不会超过 uint16_t，
    // 但作为鲁棒性检查，可以添加此行。
//    uint16_t max_scaled_speed = (uint16_t)(MOTOR_MAX_SPEED * 10 + 0.5f);
//    if (x_speed_scaled > max_scaled_speed) {
//        x_speed_scaled = max_scaled_speed;
//    }
//    if (y_speed_scaled > max_scaled_speed) {
//        y_speed_scaled = max_scaled_speed;
//    }

    /* 控制X轴电机 */
	  V_Control(Usart_below,1,x_dir,x_speed_scaled,MOTOR_ACCEL,false);
    /* 控制Y轴电机 */
	  V_Control(Usart_up,1,y_dir,y_speed_scaled,MOTOR_ACCEL,false);
}

/**
 * @brief 设置XY轴电机移动一段距离（使用位置模式）
 * @param x_distance X轴移动距离（脉冲数），正值为CW方向，负值为CCW方向
 * @param y_distance Y轴移动距离（脉冲数），正值为CW方向，负值为CCW方向
 */
void Step_Motor_Set_Pwm(int32_t x_distance, int32_t y_distance)
{
    uint8_t x_dir, y_dir;
    uint32_t x_clk, y_clk;
    uint16_t speed = MOTOR_MAX_SPEED;  /* 使用最大速度，或根据需要调整 */
    uint8_t acc = MOTOR_ACCEL;        /* 使用预定义加速度 */

    /* 设置X轴方向和脉冲数 */
    if (x_distance >= 0)
    {
        x_dir = 0; /* CW方向 */
        x_clk = (uint32_t)x_distance;
    }
    else
    {
        x_dir = 1; /* CCW方向 */
        x_clk = (uint32_t)(-x_distance); /* 取绝对值 */
    }

    /* 设置Y轴方向和脉冲数 */
    if (y_distance >= 0)
    {
        y_dir = 0; /* CW方向 */
        y_clk = (uint32_t)y_distance;
    }
    else
    {
        y_dir = 1; /* CCW方向 */
        y_clk = (uint32_t)(-y_distance); /* 取绝对值 */
    }

    /* 控制X轴电机（相对运动，不启用绝对模式） */       
    S_Control(Usart_below, 1, x_dir, speed, acc, x_clk, false, false);

    /* 控制Y轴电机（相对运动，不启用绝对模式） */
    S_Control(Usart_up, 1, y_dir, speed, acc, y_clk, false, false);
}


//	EN:电机使能控制--PA5
//	STP:脉冲引脚--PA6
//	Dir:方向引脚--PA7
// 脉冲模式初始化（相当于串口模式的速度模式，停止的话就关闭TIM3定时器使能）
// 修改CCR1_Val（PID）控制速度
//void Pulse_Init()
//{
//	// 使能GPIOA、AFIO外设时钟
//	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA | RCC_APB2Periph_GPIOB | RCC_APB2Periph_AFIO,ENABLE);
//	GPIO_InitTypeDef GPIO_InitStructure;
//	GPIO_InitStructure.GPIO_Pin=GPIO_Pin_5|GPIO_Pin_7;
//	GPIO_InitStructure.GPIO_Mode=GPIO_Mode_Out_PP;   //推挽输出
//	GPIO_InitStructure.GPIO_Speed=GPIO_Speed_50MHz;
//	GPIO_Init(GPIOA,&GPIO_InitStructure);
//	
//	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;              //  复用推挽输出
//	GPIO_InitStructure.GPIO_Pin=GPIO_Pin_6;						// 初始化TIM3引脚
//	GPIO_InitStructure.GPIO_Speed=GPIO_Speed_50MHz;
//	GPIO_Init(GPIOA,&GPIO_InitStructure);
//	//初始化为低电平
//	GPIO_ResetBits(GPIOA, GPIO_Pin_5|GPIO_Pin_6|GPIO_Pin_7);
//	
//	TIM_TimeBaseInitTypeDef TIM_TimeBaseStructure;
//	TIM_OCInitTypeDef TIM_OCInitStructure;
//	RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM3, ENABLE);//初始化具体比较输出功能的定时器3
//	TIM_TimeBaseStructure.TIM_Period=65535;//定时器计数周期
//	TIM_TimeBaseStructure.TIM_Prescaler=1-1;//预分频
//	TIM_TimeBaseStructure.TIM_ClockDivision=1-1;//时钟不分频
//	TIM_TimeBaseStructure.TIM_CounterMode=TIM_CounterMode_Up;//增计数
//	TIM_TimeBaseInit(TIM3, &TIM_TimeBaseStructure);//初始化定时器
//	
//	TIM_OCInitStructure.TIM_Pulse=CCR1_Val;//设置比较值（跳变值）
//	TIM_OCInitStructure.TIM_OCMode=TIM_OCMode_Toggle;//输出比较主动模式
//	TIM_OCInitStructure.TIM_OutputState=TIM_OutputState_Enable;//输出使能
//	TIM_OCInitStructure.TIM_OCPolarity=TIM_OCPolarity_High;//有效电平为高电平
//	
//	TIM_OC1Init(TIM3, &TIM_OCInitStructure);//初始化输出比较寄存器
//	TIM_OC1PreloadConfig(TIM3,TIM_OCPreload_Disable);//关闭预转载
//	
//	TIM_ITConfig(TIM3,TIM_IT_CC1,ENABLE);//清除中断标志
//	TIM_Cmd(TIM3, ENABLE);//打开定时器3
//	
//	NVIC_InitTypeDef NVIC_InitStructure;
//	NVIC_InitStructure.NVIC_IRQChannel=TIM3_IRQn;
//	NVIC_InitStructure.NVIC_IRQChannelSubPriority=1;//中断优先级为1
//	//NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority=0;          //  抢占优先级
//	NVIC_InitStructure.NVIC_IRQChannelCmd=ENABLE;
//	NVIC_Init(&NVIC_InitStructure);
//}
//void TIM3_IRQHandler(void)
//{
//	static uint16_t capture=0;
//	if (TIM_GetITStatus(TIM3,TIM_IT_CC1)==SET)		//通道1检测到比较事件
//	{
//		TIM_ClearITPendingBit(TIM3,TIM_IT_CC1);//清除标志位
//		capture=TIM_GetCapture1(TIM3);
//		
//		if((capture+CCR1_Val-1)<=65535)
//		{
//			TIM_SetCompare1(TIM3,capture+CCR1_Val-1);
//		}
//		else 
//		{
//			TIM_SetCompare1(TIM3,capture+CCR1_Val-1-65535);		//65535为定时器计数周期
//		}
//	}
//}
//定时器脉冲控制运动
//void Pulse_Stop_run(uint16_t Flag)
//{
//	if(Flag==1)
//	{
//		TIM_Cmd(TIM3,ENABLE);
//	}
//	if(Flag==0)
//	{
//		TIM_Cmd(TIM3,DISABLE);
//	}
//}
////电机使能（脉冲模式
//void Enable_Set(uint8_t Enable)
//{
//	if(Enable==0)
//	{
//		GPIO_ResetBits(GPIOA, GPIO_Pin_5);
//	}
//	if(Enable==1)
//	{
//		GPIO_SetBits(GPIOA,GPIO_Pin_5);
//	}
//	
//}
////改变方向（脉冲模式
//void Direct_Set(uint8_t Direct)
//{
//	if(Direct==0)
//	{
//		GPIO_ResetBits(GPIOA, GPIO_Pin_7);
//	}
//	if(Direct==1)
//	{
//		GPIO_SetBits(GPIOA,GPIO_Pin_7);
//	}
//}


/*-----函数作用:--------使能电机----------------*/
/*-----传递参数:--------电机串口号
						电机地址
						state使能状态，true为使能电机，false为关闭电机
						snF多机同步标志，false为不启用，true为启用*/
/*-----函返回值:--------地址+功能码+命令状态+校验字节-*/
//命令格式：地址+0xF3+0xAB+使能状态+多机同步标志+校验字节
void Motor_Enable(USART_TypeDef* USARTx,uint8_t addr,bool state,bool snF)
{
  uint8_t cmd[16]={0};
  cmd[0]=addr;
  cmd[1]=0xF3;                       // 功能码
  cmd[2]=0xAB;                       // 辅助码
  cmd[3]=(uint8_t)state;             // 使能状态（使能True01/不使能False00）
  cmd[4]=snF;                        // 多机同步运动标志（启用True01/不启用False00）
  cmd[5]=0x6B;                       // 校验字节（6B）
  Serial_SendArray(cmd,6,USARTx);
}

/*-----函数作用:--------设置电机速度----------------*/
/*-----传递参数:--------电机串口号
						addr电机地址
						dir方向:0为CW，其余值为CCW(逆时针旋转)
						vel速度，范围0 - 5000RPM(转每分)
						acc加速度，范围0 - 255，注意：0是直接启动
						snF多机同步标志，false为不启用，true为启用*/
/*-----函返回值:--------地址+功能码+命令状态+校验字节---*/
//命令格式：地址+0xF6+方向+速度+加速度+多机同步标志+校验字节
void V_Control(USART_TypeDef* USARTx,uint8_t addr,uint8_t dir,uint16_t vel,uint8_t acc,bool snF)
{
  uint8_t cmd[16]={0};
  cmd[0]=addr;                       // 地址
  cmd[1]=0xF6;                       // 功能码
  cmd[2]=dir;                        // 方向（00为顺时针，其余值为逆时针旋转）
  cmd[3]=(uint8_t)(vel>>8);        // 速度(RPM)高8位字节
  cmd[4]=(uint8_t)(vel>>0);        // 速度(RPM)低8位字节  例：05 DC表示速度为0x05DC=1500(转/分)
  cmd[5]=acc;                        // 加速度，注意：0是直接启动（0A->0x0A=10,加速度档位为10，曲线加减速时间计算公式见手册6.1
  cmd[6]=snF;                        // 多机同步运动标志
  cmd[7]=0x6B;                       // 校验字节
  Serial_SendArray(cmd,8,USARTx);
}

/*-----函数作用:--------设置电机位置----------------*/
/*-----传递参数:--------电机串口号
						addr电机地址
						dir方向:0为CW，其余值为CCW(逆时针旋转)
						vel速度，范围0 - 5000RPM(转每分)
						acc加速度，范围0 - 255，注意：0是直接启动
						clk脉冲数,范围0- (2^32 - 1)个
						raF相位/绝对标志，false为相对运动，true为绝对值运动
						snF多机同步标志，false为不启用，true为启用*/
/*-----函返回值:--------地址+功能码+命令状态+校验字节---*/
//命令格式：地址+0xFD+方向+速度+加速度+脉冲数+相对/绝对模式标志+多机同步标志+校验字节
void S_Control(USART_TypeDef* USARTx,uint8_t addr,uint8_t dir,uint16_t vel,uint8_t acc,uint32_t clk,bool raF,bool snF)
{
  uint8_t cmd[16]={0};
  cmd[0]=addr;
  cmd[1]=0xFD;                    // 功能码
  cmd[2]=dir;                     // 方向（00为顺时针，其余值为逆时针旋转）
  cmd[3]=(uint8_t)(vel>>8);       // 速度(RPM)高8位字节
  cmd[4]=(uint8_t)(vel>>0);       // 速度(RPM)低8位字节  例：05 DC表示速度为0x05DC=1500(转/分)
  cmd[5]=acc;                     // 加速度，注意：0是直接启动 （0A->0x0A=10,加速度档位为10，曲线加减速时间计算公式见手册6.1
  cmd[6]=(uint8_t)(clk>>24);      // 脉冲数(bit24-bit31)
  cmd[7]=(uint8_t)(clk>>16);      // 脉冲数(bit16-bit23)
  cmd[8]=(uint8_t)(clk>>8);       // 脉冲数(bit8-bit15)
  cmd[9]=(uint8_t)(clk>>0);       // 脉冲数(bit0-bit7 )  00 00 7D 00 表示脉冲数为0x00007D00=32000 个(32细分1.8电机为1800°，16细分为10圈
  cmd[10]=raF;                    // false00为相对运动，true01为绝对运动（以设定的归零点为基准）
  cmd[11]=snF;                    // 多机同步运动标志，false为不启用，true为启用
  cmd[12]=0x6B;                   // 校验字节
  Serial_SendArray(cmd,13,USARTx);
}

/*-----函数作用:--------停止电机----------------*/
/*-----传递参数:--------电机串口号
						电机地址
						snF多机同步标志，false为不启用，true为启用*/
/*-----函返回值:--------地址+功能码+命令状态+校验字节-*/
//命令格式：地址+0xFE+0x98+多机同步标志+校验字节
void Stop_Now(USART_TypeDef* USARTx,uint8_t addr, bool snF)
{
  uint8_t cmd[16] = {0};
  cmd[0]=addr;                       // 地址
  cmd[1]=0xFE;                       // 功能码
  cmd[2]=0x98;                       // 辅助码
  cmd[3]=snF;                        // 多机同步运动标志
  cmd[4]=0x6B;                       // 校验字节
  Serial_SendArray(cmd,5,USARTx);
}

/*-----函数作用:--------设置多机同步运动----------------*/
/*-----传递参数:--------电机串口号
						电机地址----------------*/
/*-----函返回值:--------地址+功能码+命令状态+校验字节-*/
//命令格式：地址+0xFF+0x66+校验字节
void Many_Motor(USART_TypeDef* USARTx,uint8_t addr)
{
	//具体控制方式见手册6.5多机通讯及同步控制
  uint8_t cmd[16]={0};
  cmd[0]=addr;
  cmd[1]=0xFF;                       // 功能码
  cmd[2]=0x66;                       // 辅助码
  cmd[3]=0x6B;                       // 校验字节
  Serial_SendArray(cmd,4,USARTx);
}

/*-----函数作用:--------将当前电机位置设置为单圈回零的零点位置----------------*/
/*-----传递参数:--------电机串口号
						电机地址
						svF是否存储标志，false为不存储，true为存储*/
/*-----函返回值:--------地址+功能码+命令状态+校验字节-*/
//命令格式：地址+0x93+0x88+是否存储标志+校验字节
void Set_zero_position(USART_TypeDef* USARTx,uint8_t addr, bool svF)
{
  uint8_t cmd[16] = {0};
  cmd[0]=addr;                       // 地址
  cmd[1]=0x93;                       // 功能码
  cmd[2]=0x88;                       // 辅助码
  cmd[3]=svF;                        // 是否存储标志，false为不存储，true为存储
  cmd[4]=0x6B;                       // 校验字节
  Serial_SendArray(cmd,5,USARTx);
}

/*-----函数作用:--------设置完原点回零后，发送该命令使电机回零----------------*/
/*-----传递参数:--------电机串口号
						addr电机地址
						o_mode回零模式，0为单圈就近回零，1为单圈方向回零，2为多圈无限位碰撞回零，3为多圈有限位开关回零
						snF多机同步标志，false为不启用，true为启用*/
/*-----函返回值:--------地址+功能码+命令状态+校验字节-*/
//命令格式：地址+0x9A+回零模式+多机同步标志+校验字节
void Return_zero(USART_TypeDef* USARTx,uint8_t addr, uint8_t o_mode, bool snF)
{
  uint8_t cmd[16] = {0};
  cmd[0] =addr;                       // 地址
  cmd[1] =0x9A;                       // 功能码
  cmd[2] =o_mode;                     // 回零模式，0为单圈就近回零，1为单圈方向回零，2为多圈无限位碰撞回零，3为多圈有限位开关回零
  cmd[3] =snF;                        // 多机同步运动标志，false为不启用，true为启用
  cmd[4] =0x6B;                       // 校验字节
  Serial_SendArray(cmd,5,USARTx);
}

/*-----函数作用:--------中断退出回零----------------*/
/*-----传递参数:--------电机串口号
						电机地址----------------*/
/*-----函返回值:--------地址+功能码+命令状态+校验字节-*/
//命令格式：地址+0x9C+0x48+校验字节
void Interrupt_return_zero(USART_TypeDef* USARTx,uint8_t addr)
{
  uint8_t cmd[16] = {0};
  cmd[0] =addr;                       // 地址
  cmd[1] =0x9C;                       // 功能码
  cmd[2] =0x48;                       // 辅助码
  cmd[3] =0x6B;                       // 校验字节
  Serial_SendArray(cmd,4,USARTx);
}

/*-----函数作用:--------读取原点回零参数--*/
/*-----传递参数:--------没写完------------*/
/*-----函返回值:--------用不上------------*/


/*-----函数作用:--------修改回零参数----------------*/
/*-----传递参数:--------电机串口号
						addr   电机地址
						svF    保存本次修改的配置参数，false00为不存储，true01为存储
						o_mode 回零模式，0为单圈就近回零，1为单圈方向回零，2为多圈无限位碰撞回零，3为多圈有限位开关回零
						o_dir  回零方向，0为CW，其余值为CCW
						o_vel  回零速度，单位：RPM（转/分钟）
						o_tm   回零超时时间，单位：毫秒
						potF   上电自动触发回零，false为不使能，true为使能*/
/*-----函返回值:--------地址+功能码+命令状态+校验字节-*/
//命令格式：地址+0x4C+0xAE+是否存储标志+原点回零参数+校验字节
void Change_tozero_parameter(USART_TypeDef* USARTx,uint8_t addr, bool svF, uint8_t o_mode, uint8_t o_dir, uint16_t o_vel, uint32_t o_tm, bool potF)
{
  uint8_t cmd[32]={0};
  uint16_t sl_vel=0;
  uint16_t sl_ma=0;
  uint16_t sl_ms=0;
  cmd[0] =addr;                       
  cmd[1] =0x4C;                       // 功能码
  cmd[2] =0xAE;                       // 辅助码
  cmd[3] =svF;                        // 是否保存本次修改的配置参数,false为不存储，true为存储
  cmd[4] =o_mode;                     // 回零模式，0为单圈就近回零，1为单圈方向回零，2为多圈无限位碰撞回零，3为多圈有限位开关回零
  cmd[5] =o_dir;                      // 回零方向(00顺时针，01逆时针)
  cmd[6] =(uint8_t)(o_vel >> 8);     // 回零速度(RPM)高8位字节
  cmd[7] =(uint8_t)(o_vel >> 0);     // 回零速度(RPM)低8位字节   05 DC表示速度为0x05DC=1500(转/分)
  cmd[8] =(uint8_t)(o_tm >> 24);     // 回零超时时间(bit24 - bit31)
  cmd[9] =(uint8_t)(o_tm >> 16);     // 回零超时时间(bit16 - bit23)
  cmd[10]=(uint8_t)(o_tm >> 8);      // 回零超时时间(bit8  - bit15)
  cmd[11]=(uint8_t)(o_tm >> 0);      // 回零超时时间(bit0  - bit7 )   0x00002710 = 回零超时时间为10000(ms)
  //===用不上↓
  cmd[12]=(uint8_t)(sl_vel >> 8);    // 无限位碰撞回零检测转速(RPM)高8位字节(这个功能用不上)
  cmd[13]=(uint8_t)(sl_vel >> 0);    // 无限位碰撞回零检测转速(RPM)低8位字节 
  cmd[14]=(uint8_t)(sl_ma >> 8);     // 无限位碰撞回零检测电流(Ma)高8位字节
  cmd[15]=(uint8_t)(sl_ma >> 0);     // 无限位碰撞回零检测电流(Ma)低8位字节 
  cmd[16]=(uint8_t)(sl_ms >> 8);     // 无限位碰撞回零检测时间(Ms)高8位字节
  cmd[17]=(uint8_t)(sl_ms >> 0);     // 无限位碰撞回零检测时间(Ms)低8位字节
  //===用不上↑
  cmd[18]=potF;                      // 上电是否自动触发回零，false为不使能，true为使能
  cmd[19]=0x6B;                      // 校验字节
  Serial_SendArray(cmd,20,USARTx);
}

/*-----函数作用:--------将当前位置角度、位置误差、脉冲数等全部清零----------------*/
/*-----传递参数:--------电机串口号
						电机地址----------------------*/
/*-----函返回值:--------地址+功能码+命令状态+校验字节-*/
//命令格式：地址+0x0A+0x6D+校验字节
void Reset_To_Zero(USART_TypeDef* USARTx,uint8_t addr)
{
  uint8_t cmd[16]={0};				 // 命令数组
  cmd[0]=addr;                       // 电机地址(在电机菜单中设置)
  cmd[1]=0x0A;                       // 功能码
  cmd[2]=0x6D;                       // 辅助码
  cmd[3]=0x6B;                       // 校验字节
  Serial_SendArray(cmd,4,USARTx);
}

/*-----函数作用:--------解除堵转保护----------------*/
/*-----传递参数:--------电机串口号
						电机地址----------------------*/
/*-----函返回值:--------地址+功能码+命令状态+校验字节-*/
void Contact_LockedRotor_Protect(USART_TypeDef* USARTx,uint8_t addr)
{
  uint8_t cmd[16]={0};
  cmd[0]=addr;                       
  cmd[1]=0x0E;                       // 功能码
  cmd[2]=0x52;                       // 辅助码
  cmd[3]=0x6B;                       // 校验字节
  Serial_SendArray(cmd,4,USARTx);
}

/*-----函数作用:--------读取电机参数----------------*/
/*-----传递参数:--------电机串口号
						电机地址----------------------*/
/*-----函返回值:--------地址+功能码+命令状态+校验字节-*/
void Read_Params(USART_TypeDef* USARTx,uint8_t addr,Motor_Parameter s)
{
  uint8_t i=0;
  uint8_t cmd[16] = {0};
  cmd[i] = addr; ++i;                   // 地址
  switch(s)                             // 功能码
  {
    case S_VER  : cmd[i] = 0x1F; ++i; break;
    case S_RL   : cmd[i] = 0x20; ++i; break;
    case S_PID  : cmd[i] = 0x21; ++i; break;
    case S_VBUS : cmd[i] = 0x24; ++i; break;
    case S_CPHA : cmd[i] = 0x27; ++i; break;
    case S_ENCL : cmd[i] = 0x31; ++i; break;
    case S_TPOS : cmd[i] = 0x33; ++i; break;
    case S_VEL  : cmd[i] = 0x35; ++i; break;
    case S_CPOS : cmd[i] = 0x36; ++i; break;
    case S_PERR : cmd[i] = 0x37; ++i; break;
    case S_FLAG : cmd[i] = 0x3A; ++i; break;
    case S_ORG  : cmd[i] = 0x3B; ++i; break;
    case S_Conf : cmd[i] = 0x42; ++i; cmd[i] = 0x6C; ++i; break;
    case S_State: cmd[i] = 0x43; ++i; cmd[i] = 0x7A; ++i; break;
    default: break;
  }

  cmd[i] = 0x6B; ++i;                   // 校验字节
  Serial_SendArray(cmd,i,USARTx);
}

/*-----函数作用:--------设置电机开环/闭环控制模式----------------*/
/*-----传递参数:--------电机串口号
						addr:电机地址
						svF:是否存储标志，false为不存储，true为存储
						ctrl_mode：控制模式（对应屏幕上的P_Pul菜单），0是关闭脉冲输入引脚，1是开环模式，2是闭环模式，3是让En端口复用为多圈限位开关输入引脚，Dir端口复用为到位输出高电平功能*/
/*-----函返回值:--------地址+功能码+命令状态+校验字节-*/
void Modify_Ctrl_Mode(USART_TypeDef* USARTx,uint8_t addr, bool svF, uint8_t ctrl_mode)
{
  uint8_t cmd[16] = {0};
  cmd[0] =  addr;                       // 地址
  cmd[1] =  0x46;                       // 功能码
  cmd[2] =  0x69;                       // 辅助码
  cmd[3] =  svF;                        // 是否存储标志，false为不存储，true为存储
  cmd[4] =  ctrl_mode;                  // 控制模式（对应屏幕上的P_Pul菜单），0是关闭脉冲输入引脚，1是开环模式，2是闭环模式，3是让En端口复用为多圈限位开关输入引脚，Dir端口复用为到位输出高电平功能
  cmd[5] =  0x6B;                       // 校验字节
  Serial_SendArray(cmd,6,USARTx);
}


/*-----函数作用:--------触发编码器校准----*/
/*-----传递参数:--------没写完------------*/
/*-----函返回值:--------------------------*/






/*========================================serial文件中已配置
void Stepmotor_Uart_Init(void)
{
	// PA9-USART1_TX
	GPIO_InitTypeDef  GPIO_InitStructure;
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_9;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;		//  复用推挽输出
	GPIO_Init(GPIOA, &GPIO_InitStructure);
	// PA10-USART1_RX
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_10;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;		//  上拉输入
	GPIO_Init(GPIOA, &GPIO_InitStructure);
	// 配置USART
	USART_InitTypeDef USART_InitStructure;
	USART_InitStructure.USART_BaudRate=115200;                   //  设置波特率,函数内部会自动根据我们设置的波特率对应分频系数
	USART_InitStructure.USART_HardwareFlowControl=USART_HardwareFlowControl_None;   //  硬件流控制(数据接收不过来通过硬件暂停输入)
	USART_InitStructure.USART_Mode=USART_Mode_Tx|USART_Mode_Rx;//  串口模式  如果需要发送和接收就USART_Mode_Tx|USART_Mode_Rx
	USART_InitStructure.USART_Parity=USART_Parity_No;          //  校验位(No无校验,Odd奇校验,Even偶校验)
	USART_InitStructure.USART_StopBits=USART_StopBits_1;       //  选择停止位长度
	USART_InitStructure.USART_WordLength=USART_WordLength_8b;  //  选择数据长度
	USART_Init(USART1,&USART_InitStructure);
	
	// 配置中断(TICongfig NVIC)用来接收数据(发送数据就不需要这一步)
	USART_ITConfig(USART1,USART_IT_RXNE,ENABLE);               //  第二个参数选择USART中断源,RXEN当接受到数据时开启到NVIC的中断
	TIM_ITConfig(TIM1,TIM_IT_Update,ENABLE);
	NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);            //  NVIC分组2个抢占优先级2个响应优先级
	NVIC_InitTypeDef NVIC_InitStructure;
	NVIC_InitStructure.NVIC_IRQChannel=USART1_IRQn;            //  中断通道
	NVIC_InitStructure.NVIC_IRQChannelCmd=ENABLE;              //  使能
	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority=2;    //  中断通道的抢占优先级
	NVIC_InitStructure.NVIC_IRQChannelSubPriority=2;           //  中断通道的响应优先级
	NVIC_Init(&NVIC_InitStructure);
	
	//第五步USART使能(发送数据直接开启cmd即可)
	USART_Cmd(USART1,ENABLE);
}
====================================*/
