#ifndef _MENU_H_
#define _MENU_H_
/* 定义菜单结构体 */
typedef struct MenuItem
{
	char name[15];					//菜单项的名字    name[]大小需要根据菜单大小调整，大了超出内存无法显示，小了无法显示完整字模
	uint8_t MenuItemGrade;			//菜单项的级数
	struct MenuItem *next;			//指向当级下一个菜单项的指针
	struct MenuItem *back;			//指向上级父菜单项的指针
	struct MenuItem *sub;			//指向下级子菜单项的指针
	void (*action)();				//指向执行函数的指针
}MenuItem;

extern uint8_t Mode;
extern MenuItem *head;
extern uint8_t Show_falg;
void Menu_Init(void);
void MenuRuning(MenuItem* CurrentMenu);
MenuItem* CreateMenuItem(char *name, void(*action)(), uint8_t MenuItemGrade, MenuItem *backMenu);
void FreeSpace(MenuItem *current);
#endif

