#include "Head.H"

// 全局变量定义
static TrackingConfig_t tracking_config = {
    .target_lost_threshold = TARGET_LOST_THRESHOLD_MS,
    .laser_check_interval = LASER_CHECK_INTERVAL_MS,
    .max_laser_error_count = MAX_LASER_ERROR_COUNT,
    .enable_status_display = 1,
    .tracking_precision = TRACKING_PRECISION_PIXELS
};

static TrackingStatus_t tracking_status = {
    .current_state = TRACK_IDLE,
    .is_tracking = 0,
    .target_detected = 0,
    .state_timer = 0,
    .target_lost_timer = 0,
    .last_error = TRACK_ERROR_NONE,
    .last_valid_target_x = 320.0f,
    .last_valid_target_y = 240.0f,
    .last_valid_purple_x = 320.0f,
    .last_valid_purple_y = 240.0f,
    .laser_manager = {0, 0, 0},
    .total_tracking_time = 0,
    .target_lost_count = 0,
    .successful_tracks = 0
};

// 主跟踪函数 (10ms周期调用)
void Target_Tracking(void)
{
    light_position light;
    
    // 更新状态计时器
    tracking_status.state_timer += 10; // 10ms周期
    
    // 更新总跟踪时间
    if(tracking_status.is_tracking) {
        tracking_status.total_tracking_time += 10;
    }
    
    // 获取激光数据
    if(Get_light_data(&light)) {
        // 数据有效性检查
        if(Validate_Light_Data(&light)) {
            // 更新最后有效数据
            Update_Last_Valid_Data(&light);
            
            // 状态机处理
            Tracking_State_Machine(&light);
            
            // 执行PID控制
            if(tracking_status.current_state == TRACK_FOLLOWING) {
                Execute_PID_Control(&light);
            }
        } else {
            // 数据无效，使用最后有效数据
            Handle_Tracking_Error(TRACK_ERROR_DATA_INVALID);
        }
    } else {
        // 数据获取失败
        Handle_Tracking_Error(TRACK_ERROR_COMM_TIMEOUT);
    }
    
    // 激光管理更新
    Laser_Manager_Update();
    
    // 显示状态信息
    if(tracking_config.enable_status_display) {
        Display_Tracking_Status();
    }
}

// 状态机处理
static void Tracking_State_Machine(light_position* light)
{
    switch(tracking_status.current_state) {
        case TRACK_IDLE:
            // 空闲状态：等待启动指令
            tracking_status.is_tracking = 0;
            tracking_status.target_detected = 0;
            break;
            
        case TRACK_SEARCHING:
            // 搜索状态：等待目标出现
            tracking_status.is_tracking = 1;
            tracking_status.target_detected = 0;
            
            // 确保激光开启
            if(!tracking_status.laser_manager.laser_enabled) {
                JG_Ton();
                tracking_status.laser_manager.laser_enabled = 1;
            }
            
            // 检测目标
            if(light->target_flag == 1) {
                // 检测到目标，切换到跟踪状态
                tracking_status.current_state = TRACK_FOLLOWING;
                tracking_status.target_detected = 1;
                tracking_status.target_lost_timer = 0;
                tracking_status.successful_tracks++;
                tracking_status.state_timer = 0;
            }
            break;
            
        case TRACK_FOLLOWING:
            // 跟踪状态：连续跟踪目标
            tracking_status.is_tracking = 1;
            
            if(light->target_flag == 1 && light->purple_flag == 1) {
                // 目标和激光都存在，正常跟踪
                tracking_status.target_detected = 1;
                tracking_status.target_lost_timer = 0;
                
                // 确保激光开启
                if(!tracking_status.laser_manager.laser_enabled) {
                    JG_Ton();
                    tracking_status.laser_manager.laser_enabled = 1;
                }
            } else {
                // 目标或激光丢失
                tracking_status.target_lost_timer += 10; // 10ms周期
                
                if(tracking_status.target_lost_timer >= tracking_config.target_lost_threshold) {
                    // 目标丢失超过阈值，切换到搜索状态
                    tracking_status.current_state = TRACK_SEARCHING;
                    tracking_status.target_detected = 0;
                    tracking_status.target_lost_count++;
                    tracking_status.state_timer = 0;
                    Handle_Tracking_Error(TRACK_ERROR_TARGET_LOST);
                }
            }
            break;
            
        default:
            // 未知状态，重置到空闲
            tracking_status.current_state = TRACK_IDLE;
            break;
    }
}

// 数据有效性检查
static uint8_t Validate_Light_Data(light_position* light)
{
    // 检查坐标范围
    if(light->purple_x < COORD_VALID_MIN || light->purple_x > COORD_VALID_MAX_X ||
       light->purple_y < COORD_VALID_MIN || light->purple_y > COORD_VALID_MAX_Y ||
       light->target_x < COORD_VALID_MIN || light->target_x > COORD_VALID_MAX_X ||
       light->target_y < COORD_VALID_MIN || light->target_y > COORD_VALID_MAX_Y) {
        return 0; // 坐标越界
    }
    
    return 1; // 数据有效
}

// 更新最后有效数据
static void Update_Last_Valid_Data(light_position* light)
{
    if(light->target_flag == 1) {
        tracking_status.last_valid_target_x = light->target_x;
        tracking_status.last_valid_target_y = light->target_y;
    }
    
    if(light->purple_flag == 1) {
        tracking_status.last_valid_purple_x = light->purple_x;
        tracking_status.last_valid_purple_y = light->purple_y;
    }
}

// 执行PID控制
static void Execute_PID_Control(light_position* light)
{
    if(light->target_flag == 1 && light->purple_flag == 1) {
        // 设置目标坐标
        app_pid_set_target((int)light->target_x, (int)light->target_y);
        
        // 更新当前位置
        app_pid_update_position((int)light->purple_x, (int)light->purple_y);
        
        // 执行PID控制
        app_pid_calc();
    } else {
        // 使用最后有效数据继续控制
        app_pid_set_target((int)tracking_status.last_valid_target_x, 
                          (int)tracking_status.last_valid_target_y);
        app_pid_update_position((int)tracking_status.last_valid_purple_x, 
                               (int)tracking_status.last_valid_purple_y);
        app_pid_calc();
    }
}

// 激光管理更新
static void Laser_Manager_Update(void)
{
    tracking_status.laser_manager.laser_check_timer += 10; // 10ms周期
    
    // 定期检查激光状态
    if(tracking_status.laser_manager.laser_check_timer >= tracking_config.laser_check_interval) {
        tracking_status.laser_manager.laser_check_timer = 0;
        
        // 在跟踪状态下确保激光开启
        if(tracking_status.current_state != TRACK_IDLE) {
            if(!tracking_status.laser_manager.laser_enabled) {
                JG_Ton();
                tracking_status.laser_manager.laser_enabled = 1;
                tracking_status.laser_manager.laser_error_count++;
                
                // 检查激光错误次数
                if(tracking_status.laser_manager.laser_error_count >= tracking_config.max_laser_error_count) {
                    Handle_Tracking_Error(TRACK_ERROR_LASER_FAIL);
                }
            }
        }
    }
}

// 错误处理
static void Handle_Tracking_Error(TrackingError_t error)
{
    tracking_status.last_error = error;
    
    switch(error) {
        case TRACK_ERROR_TARGET_LOST:
            // 目标丢失，切换到搜索状态
            if(tracking_status.current_state == TRACK_FOLLOWING) {
                tracking_status.current_state = TRACK_SEARCHING;
            }
            break;
            
        case TRACK_ERROR_LASER_FAIL:
            // 激光故障，停止跟踪
            Target_Tracking_Stop();
            break;
            
        case TRACK_ERROR_MOTOR_FAIL:
            // 电机故障，立即停止
            Target_Tracking_Stop();
            break;
            
        case TRACK_ERROR_DATA_INVALID:
            // 数据无效，继续使用最后有效数据
            break;
            
        case TRACK_ERROR_COMM_TIMEOUT:
            // 通信超时，暂停PID控制
            break;
            
        default:
            break;
    }
}

// 显示跟踪状态
static void Display_Tracking_Status(void)
{
    static uint32_t display_timer = 0;
    display_timer += 10; // 10ms周期
    
    // 每100ms更新一次显示
    if(display_timer >= 100) {
        display_timer = 0;
        
        switch(tracking_status.current_state) {
            case TRACK_IDLE:
                OLED_ShowString(0, 0, "Track_Idle", OLED_8X16);
                break;
                
            case TRACK_SEARCHING:
                OLED_ShowString(0, 0, "Searching..", OLED_8X16);
                break;
                
            case TRACK_FOLLOWING:
                OLED_ShowString(0, 0, "Following", OLED_8X16);
                if(tracking_status.target_detected) {
                    OLED_ShowString(0, 16, "Target_OK", OLED_8X16);
                } else {
                    OLED_ShowString(0, 16, "Target_Lost", OLED_8X16);
                }
                break;
        }
        
        // 显示统计信息
        OLED_Printf(0, 32, OLED_8X16, "Time:%ds", tracking_status.total_tracking_time / 1000);
        OLED_Printf(0, 48, OLED_8X16, "Lost:%d", tracking_status.target_lost_count);
        
        OLED_Update();
    }
}

// 控制接口实现
void Target_Tracking_Start(void)
{
    if(tracking_status.current_state == TRACK_IDLE) {
        tracking_status.current_state = TRACK_SEARCHING;
        tracking_status.is_tracking = 1;
        tracking_status.state_timer = 0;
        tracking_status.target_lost_timer = 0;
        tracking_status.last_error = TRACK_ERROR_NONE;
        
        // 开启激光
        JG_Ton();
        tracking_status.laser_manager.laser_enabled = 1;
        tracking_status.laser_manager.laser_error_count = 0;
    }
}

void Target_Tracking_Stop(void)
{
    tracking_status.current_state = TRACK_IDLE;
    tracking_status.is_tracking = 0;
    tracking_status.target_detected = 0;
    
    // 关闭激光
    JG_Toff();
    tracking_status.laser_manager.laser_enabled = 0;
    
    // 停止电机
    Stop_Now(Usart_below, 1, false);
    Stop_Now(Usart_up, 1, false);
}

void Target_Tracking_Reset(void)
{
    Target_Tracking_Stop();
    
    // 重置所有状态
    tracking_status.state_timer = 0;
    tracking_status.target_lost_timer = 0;
    tracking_status.last_error = TRACK_ERROR_NONE;
    tracking_status.laser_manager.laser_check_timer = 0;
    tracking_status.laser_manager.laser_error_count = 0;
    
    // 重置统计信息
    Target_Reset_Statistics();
}

void Target_Tracking_Pause(void)
{
    if(tracking_status.current_state != TRACK_IDLE) {
        // 停止电机但保持激光
        Stop_Now(Usart_below, 1, false);
        Stop_Now(Usart_up, 1, false);
        tracking_status.is_tracking = 0;
    }
}

void Target_Tracking_Resume(void)
{
    if(tracking_status.current_state != TRACK_IDLE) {
        tracking_status.is_tracking = 1;
    }
}

// 状态查询接口实现
uint8_t Target_Is_Tracking(void)
{
    return tracking_status.is_tracking;
}

uint8_t Target_Has_Target(void)
{
    return tracking_status.target_detected;
}

TrackingState_t Target_Get_State(void)
{
    return tracking_status.current_state;
}

TrackingError_t Target_Get_Last_Error(void)
{
    return tracking_status.last_error;
}

// 配置和状态接口实现
void Target_Set_Config(TrackingConfig_t* config)
{
    if(config != NULL) {
        tracking_config = *config;
    }
}

TrackingConfig_t* Target_Get_Config(void)
{
    return &tracking_config;
}

TrackingStatus_t* Target_Get_Status(void)
{
    return &tracking_status;
}

// 统计信息接口实现
uint32_t Target_Get_Tracking_Time(void)
{
    return tracking_status.total_tracking_time;
}

uint32_t Target_Get_Lost_Count(void)
{
    return tracking_status.target_lost_count;
}

void Target_Reset_Statistics(void)
{
    tracking_status.total_tracking_time = 0;
    tracking_status.target_lost_count = 0;
    tracking_status.successful_tracks = 0;
}
