#include <stdbool.h>
#ifndef _STEPPING_MOTOR_H_
#define _STEPPING_MOTOR_H_

//定于取反功能
#define	ABS(x)   ((x) > 0 ? (x) : -(x))

/* 电机控制宏定义 */
#define   Usart_below    USART2				// 使用串口2发送下面电机；需要在Init中也修改相应的配置
#define   Usart_up  	 USART3				// 使用串口3发送上面电机
#define MOTOR_MAX_SPEED     3               // 电机最大转速(RPM)
#define MOTOR_ACCEL         0             // 电机加速度(0表示直接启动)
#define MOTOR_SYNC_FLAG     false         // 电机同步标志
//#define MOTOR_MAX_ANGLE     50            // 电机最大角度限制(±50°)

//脉冲翻转的计数
#define CCR1_Val 200			//设置电机速度0-65535，越低越快(翻转模式


//读取的电机参数类别
typedef enum {
	S_VER   = 0,			/* 读取固件版本和对应的硬件版本 */
	S_RL    = 1,			/* 读取读取相电阻和相电感 */
	S_PID   = 2,			/* 读取PID参数 */
	S_VBUS  = 3,			/* 读取总线电压 */
	S_CPHA  = 5,			/* 读取相电流 */
	S_ENCL  = 7,			/* 读取经过线性化校准后的编码器值 */
	S_TPOS  = 8,			/* 读取电机目标位置角度 */
	S_VEL   = 9,			/* 读取电机实时转速 */
	S_CPOS  = 10,			/* 读取电机实时位置角度 */
	S_PERR  = 11,			/* 读取电机位置误差角度 */
	S_FLAG  = 13,			/* 读取使能/到位/堵转状态标志位 */
	S_Conf  = 14,			/* 读取驱动参数 */
	S_State = 15,			/* 读取系统状态参数 */
	S_ORG   = 16,     		/* 读取正在回零/回零失败状态标志位 */
}Motor_Parameter;

/*开发函数*/
void My_motor_Init(void);
void Motor_Set_Speed(int8_t x_percent, int8_t y_percent);
void Step_Motor_Set_Pwm(int32_t x_distance, int32_t y_distance);
void Step_Motor_Set_Speed_my(float x_rpm, float y_rpm);

/*脉冲控制模式函数*/
//void Pulse_Init(void);
//void Pulse_Stop_run(uint16_t Flag);
//void Enable_Set(uint8_t Enable);
//void Direct_Set(uint8_t Direct);

/*串口控制模式函数*/
void Motor_Enable(USART_TypeDef* USARTx,uint8_t addr,bool state,bool snF);      // 电机使能控制
// 速度模式控制
void V_Control(USART_TypeDef* USARTx,uint8_t addr, uint8_t dir, uint16_t vel, uint8_t acc, bool snF); // 速度模式控制
// 位置模式控制
void S_Control(USART_TypeDef* USARTx,uint8_t addr, uint8_t dir, uint16_t vel, uint8_t acc, uint32_t clk, bool raF, bool snF);
void Stop_Now(USART_TypeDef* USARTx,uint8_t addr, bool snF); 						// 让电机立即停止运动s
void Many_Motor(USART_TypeDef* USARTx,uint8_t addr); 								// 触发多机同步开始运动
void Set_zero_position(USART_TypeDef* USARTx,uint8_t addr, bool svF); 			// 设置挡圈回零的零点位置
void Return_zero(USART_TypeDef* USARTx,uint8_t addr, uint8_t o_mode, bool snF); 	// 发送命令触发回零
void Interrupt_return_zero(USART_TypeDef* USARTx,uint8_t addr); 					// 强制中断并退出回零
// 修改回零参数
void Change_tozero_parameter(USART_TypeDef* USARTx,uint8_t addr, bool svF, uint8_t o_mode, uint8_t o_dir, uint16_t o_vel, uint32_t o_tm, bool potF);
void Reset_To_Zero(USART_TypeDef* USARTx,uint8_t addr); 							// 将当前位置角度、位置误差、脉冲数等全部清零
void Contact_LockedRotor_Protect(USART_TypeDef* USARTx,uint8_t addr); 			// 解除堵转保护
void Read_Params(USART_TypeDef* USARTx,uint8_t addr, Motor_Parameter s); 			// 读取电机各项参数
void Modify_Ctrl_Mode(USART_TypeDef* USARTx,uint8_t addr, bool svF, uint8_t ctrl_mode); 				// 发送命令修改开环/闭环控制模式

#endif
