#include "stm32f10x.h"               
#include "fifo.H"
#include <stdio.h>
#include <string.H>
#include <stdarg.h>
#include <stdbool.h>

/*还没有写另一个电机的串口接收，如果插入单片机会导致bug*/

void Serial_Init(void)
{
	//第一步开启USART,GPIO时钟
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA|RCC_APB2Periph_GPIOB,ENABLE);
	
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_USART1,ENABLE);		//摄像头串口
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART2,ENABLE);		//下面的步进电机
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART3,ENABLE);		//上面的步进电机
	
	//第二步GPIO配置 TX复用输出 RX输入
	GPIO_InitTypeDef GPIO_InitStructure;
	
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;            //  TX复用推挽输出(GPIO口被用作第二功能时的配置情况,即并非作为通用IO口使用)
	GPIO_InitStructure.GPIO_Pin=GPIO_Pin_9|GPIO_Pin_2;
	GPIO_InitStructure.GPIO_Speed=GPIO_Speed_50MHz;
	GPIO_Init(GPIOA,&GPIO_InitStructure);
	
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;              //  RX上拉输入
	GPIO_InitStructure.GPIO_Pin=GPIO_Pin_10|GPIO_Pin_3;
	GPIO_InitStructure.GPIO_Speed=GPIO_Speed_50MHz;
	GPIO_Init(GPIOA,&GPIO_InitStructure);
	
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;            //  TX复用推挽输出(GPIO口被用作第二功能时的配置情况,即并非作为通用IO口使用)
	GPIO_InitStructure.GPIO_Pin=GPIO_Pin_10;
	GPIO_InitStructure.GPIO_Speed=GPIO_Speed_50MHz;
	GPIO_Init(GPIOB,&GPIO_InitStructure);
	
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;              //  RX上拉输入
	GPIO_InitStructure.GPIO_Pin=GPIO_Pin_11;
	GPIO_InitStructure.GPIO_Speed=GPIO_Speed_50MHz;
	GPIO_Init(GPIOB,&GPIO_InitStructure);
	
	//第三步配置USART
	USART_InitTypeDef USART_InitStructure;
	USART_InitStructure.USART_BaudRate=115200;                   //  设置波特率,函数内部会自动根据我们设置的波特率对应分频系数
	USART_InitStructure.USART_HardwareFlowControl=USART_HardwareFlowControl_None;   //  硬件流控制(数据接收不过来通过硬件暂停输入)
	USART_InitStructure.USART_Mode=USART_Mode_Tx|USART_Mode_Rx;//  串口模式  如果需要发送和接收就USART_Mode_Tx|USART_Mode_Rx
	USART_InitStructure.USART_Parity=USART_Parity_No;          //  校验位(No无校验,Odd奇校验,Even偶校验)
	USART_InitStructure.USART_StopBits=USART_StopBits_1;       //  选择停止位长度
	USART_InitStructure.USART_WordLength=USART_WordLength_8b;  //  选择数据长度
	USART_Init(USART1,&USART_InitStructure);
	USART_Init(USART2,&USART_InitStructure);
	USART_Init(USART3,&USART_InitStructure);
	
	USART1->SR; USART1->DR;
	USART_ClearITPendingBit(USART1, USART_IT_RXNE);
	
	USART2->SR; USART2->DR;
	USART_ClearITPendingBit(USART2, USART_IT_RXNE);
	
	USART3->SR; USART3->DR;
	USART_ClearITPendingBit(USART3, USART_IT_RXNE);
	
	//第四步配置中断(TICongfig NVIC)用来接收数据(发送数据就不需要这一步)
	USART_ITConfig(USART1,USART_IT_RXNE,ENABLE);               //  第二个参数选择USART中断源,RXEN当接受到数据时开启到NVIC的中断
	USART_ITConfig(USART2,USART_IT_RXNE,ENABLE);
	USART_ITConfig(USART2,USART_IT_IDLE,ENABLE);				//  串口2空闲中断
	USART_ITConfig(USART3,USART_IT_RXNE,ENABLE);
	USART_ITConfig(USART3,USART_IT_IDLE,ENABLE);				//  串口3空闲中断

    /* Configure one bit for preemption priority -------------------------------- */  
	NVIC_InitTypeDef NVIC_InitStructure; 
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);  //  NVIC分组x个抢占优先级x个响应优先级
      
    /* UART1 摄像头串口-------------------------------------------------------------------- */  
    NVIC_InitStructure.NVIC_IRQChannel = USART1_IRQn;  
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 0;  	//抢占优先级
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;  		//响应优先级
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;  
    NVIC_Init(&NVIC_InitStructure);
  
    /* UART2 下面的步进电机-------------------------------------------------------------------- */  
    NVIC_InitStructure.NVIC_IRQChannel = USART2_IRQn;  
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority=1;  
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;  
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;  
    NVIC_Init(&NVIC_InitStructure); 
	
	/* UART3 上面的步进电机-------------------------------------------------------------------- */  
    NVIC_InitStructure.NVIC_IRQChannel = USART3_IRQn;  
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority =2;  
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;  
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;  
    NVIC_Init(&NVIC_InitStructure); 
	
	USART_Cmd(USART1,ENABLE);
	USART_Cmd(USART2,ENABLE);
	USART_Cmd(USART3,ENABLE);
}

/*------------------------------------------*/

//优化思路:使用DMA或双缓冲接收串口数据,避免处理不及时丢失数据

// 激光坐标数据结构
typedef struct {
    float purple_x;     // 紫色激光X坐标
    float purple_y;     // 紫色激光Y坐标
    float target_x;     // 目标点X坐标
    float target_y;     // 目标点Y坐标
    uint8_t purple_flag;// 紫色激光存在标志 (1存在, 0不存在)
    uint8_t target_flag;// 目标点存在标志 (1存在, 0不存在)
} light_position;

// 数据包类型枚举
typedef enum {
    DATA_TYPE_NONE = 0,
    DATA_TYPE_LASER = 1    // 激光坐标数据包
} DataPacketType_t;

uint8_t ByteRecv;
uint8_t rx_buffer[4];   //  暂时存储(单缓存区)
uint8_t Rx_flag;		//  如果连续接收数据包，程序处理不及时，会导致数据包错位等情况，利用Rx_flag在处理部分置1来解决

// 双缓冲串口接收
#define BUFFER_SIZE 36		//缓冲区大小：支持最大数据包36字节
typedef struct
{
    uint8_t buffer[BUFFER_SIZE];
    uint8_t write_index;
    uint8_t read_index;
    uint8_t data_ready;				//数据可调用标志位
    DataPacketType_t packet_type;  // 数据包类型
    uint8_t packet_length;         // 实际数据包长度
} DoubleBuffer_t;


// 双缓冲串口接收实现
DoubleBuffer_t rx_double_buffer = {0};		// 定义结构体声明
static uint8_t buffer1[BUFFER_SIZE];		// 数据缓存区1
static uint8_t buffer2[BUFFER_SIZE];		// 数据缓冲区2
static uint8_t* active_buffer=buffer1;		// act指向buffer1
static uint8_t* processing_buffer=buffer2;	// pro指向buffer2
//双缓冲初始化函数
void DoubleBuffer_Init(void)
{
	rx_double_buffer.write_index = 0;
	rx_double_buffer.read_index = 0;
	rx_double_buffer.data_ready = 0;
	rx_double_buffer.packet_type = DATA_TYPE_NONE;
	rx_double_buffer.packet_length = 0;
}
//解析获取缓冲区激光参数
uint8_t Get_light_data(light_position* laser_data)
{
	if(rx_double_buffer.data_ready && rx_double_buffer.packet_type == DATA_TYPE_LASER)
	{
		// 从 处理缓冲区 解析完整数据包
		uint8_t* data_ptr=processing_buffer;
		// 解析状态字节(第1个字节)
		uint8_t status_byte = data_ptr[0];
		laser_data->purple_flag = status_byte & 0x01;        // bit0: 紫色激光存在标志
		laser_data->target_flag = (status_byte >> 1) & 0x01; // bit1: 目标点存在标志
		// 解析坐标数据(从第2个字节开始，4个float，小端模式)
		memcpy(&laser_data->purple_x, &data_ptr[1], sizeof(float));   // 紫色X坐标
		memcpy(&laser_data->purple_y, &data_ptr[5], sizeof(float));   // 紫色Y坐标
		memcpy(&laser_data->target_x, &data_ptr[9], sizeof(float)); // 目标点X坐标
		memcpy(&laser_data->target_y, &data_ptr[13], sizeof(float));// 目标点Y坐标

		rx_double_buffer.data_ready = 0;
		return 1;
	}
	return 0;
}

//数据包类型
DataPacketType_t Get_packet_type(void)
{
	return rx_double_buffer.packet_type;
}

/***@brief 中断标志函数,接收到值返回OK(1),没有接收到值返回NO(0)***/
uint8_t Serial_GetRxflag_1(void)
{
	if(Rx_flag==1)
	{
		Rx_flag=0;
		return 1;
	}
	return 0;
}
/*摄像头接收串口*/
//接收中断函数(在md启动文件找该中断函数)
void USART1_IRQHandler(void)
{
	static uint8_t Rxstate=0;           //  接收机状态
	static uint8_t temp=0;              //  接收到第几个
	static DataPacketType_t current_packet_type = DATA_TYPE_NONE;  // 当前数据包类型
	static uint8_t expected_data_length = 0;    // 期望的数据长度

	if(USART_GetFlagStatus(USART1,USART_IT_RXNE)==SET)
	{
		ByteRecv=USART_ReceiveData(USART1);
		switch(Rxstate)
		{
			case 0://等待包头
				if(ByteRecv==0xEE)  // 激光数据包头
				{
					Rxstate=1;
					current_packet_type = DATA_TYPE_LASER;
				}
				break;
			case 1://接收第二个包头
				if(current_packet_type == DATA_TYPE_LASER && ByteRecv==0xFF)
				{
					Rxstate=2;
					temp=0;
					// 设置期望的数据长度
					expected_data_length = 17;  // 状态1 + 坐标16
				}
				else
				{	// 若包头错误恢复状态机
					Rxstate=0;
					current_packet_type = DATA_TYPE_NONE;
				}
				break;
			case 2://接收数据
				active_buffer[temp++]=ByteRecv;
				//active_buffer[temp++]=ByteRecv双缓存；rx_buffer[4][temp++]=ByteRecv单缓冲
				if(temp == expected_data_length)  // 根据数据包类型判断长度
				{
					Rxstate=3;
				}
				break;
			case 3://接收第一个包尾
				if(current_packet_type == DATA_TYPE_LASER && ByteRecv==0xAA)
				{
					Rxstate=4;
				}
				else
				{	// 若包尾错误恢复状态机
					Rxstate=0;
					current_packet_type = DATA_TYPE_NONE;
				}
				break;
			case 4://包尾结束 - 双缓冲切换
				if(current_packet_type == DATA_TYPE_LASER && ByteRecv==0xBB)
				{
					// 数据接收完成，切换缓冲区(地址交换)
					uint8_t* temp_ptr=active_buffer;		//将act_buff的地址暂存
					active_buffer=processing_buffer;		//act_buff指向pro_buff的地址
					processing_buffer=temp_ptr;				//pro_buff指向act_buff暂存的地址

					// 设置数据包信息
					rx_double_buffer.packet_type = current_packet_type;
					rx_double_buffer.packet_length = expected_data_length;
					rx_double_buffer.data_ready = 1;		//数据接收好标志位
					Rx_flag=1;

					/*单缓存
					memcpy(&error,rx_buffer,sizeof(float));   // 将4字节转换为float存放在error中
					Rx_flag=1;
					*/
				}
				// 无论包尾是否正确都复位状态机
				Rxstate=0;
				current_packet_type = DATA_TYPE_NONE;
				break;
		}
		USART_ClearITPendingBit(USART1,USART_IT_RXNE);         //  清除中断标志位(硬件也会自动清0)
	}
}

__IO bool rxFrameFlag=false;	//串口接收标志
__IO uint8_t rxCmd[FIFO_SIZE]={0};//串口数组返回存储
__IO uint8_t rxCount = 0;

__IO bool rxFrameFlag_3=false;	//串口3接收标志
__IO uint8_t rxCmd_3[FIFO_SIZE]={0};//串口3数组返回存储
__IO uint8_t rxCount_3 = 0;

uint8_t Serial_GetRxflag_2(void)
{
	if(rxFrameFlag==true)
	{
		rxFrameFlag=false;
		return true;
	}
	return false;
}

uint8_t Serial_GetRxflag_3(void)
{
	if(rxFrameFlag_3==true)
	{
		rxFrameFlag_3=false;
		return true;
	}
	return false;
}

void USART2_IRQHandler(void)
{
	__IO uint16_t i = 0;
	//串口接收中断
	if(USART_GetITStatus(USART2, USART_IT_RXNE)==SET)
	{
		// 未完成一帧数据接收，数据进入缓冲队列
		fifo_In((uint8_t)USART2->DR);
		// 清除串口接收中断
		USART_ClearITPendingBit(USART2, USART_IT_RXNE);
	}
	//串口空闲
	else if(USART_GetITStatus(USART2, USART_IT_IDLE) != RESET)
	{
		// 先读SR再读DR，清除IDLE中断
		USART2->SR; USART2->DR;
		// 提取一帧数据命令
		rxCount=fifo_queueLength();
		for(i=0;i<rxCount;i++)
		{rxCmd[i]=fifo_Out();}
		// 一帧数据接收完成，置位帧标志位
		rxFrameFlag=true;
	}
}

void USART3_IRQHandler(void)
{
	__IO uint16_t i = 0;
	//串口接收中断
	if(USART_GetITStatus(USART3, USART_IT_RXNE)==SET)
	{
		// 未完成一帧数据接收，数据进入缓冲队列
		fifo_In_3((uint8_t)USART3->DR);
		// 清除串口接收中断
		USART_ClearITPendingBit(USART3, USART_IT_RXNE);
	}
	//串口空闲
	else if(USART_GetITStatus(USART3, USART_IT_IDLE) != RESET)
	{
		// 先读SR再读DR，清除IDLE中断
		USART3->SR; USART3->DR;
		// 提取一帧数据命令
		rxCount_3=fifo_queueLength_3();
		for(i=0;i<rxCount_3;i++)
		{rxCmd_3[i]=fifo_Out_3();}
		// 一帧数据接收完成，置位帧标志位
		rxFrameFlag_3=true;
	}
}



/*------------------------------------------*/

/**
 * @brief  串口发送字节
 * @param  要发送的字节
 * @retval 无
 */
void Serial_SendByte(USART_TypeDef* USARTx,uint8_t Byte)
{
	USART_SendData(USARTx,Byte);                                //  数据最终通向TDR寄存器再传递到发送移位寄存器,自动移位发送数据
	/*TDR数据转移到移位寄存器才能继续转运,不然数据被覆盖*/         
	while(USART_GetFlagStatus(USARTx,USART_FLAG_TXE) == RESET); //  USART_FLAG_TXE发送数据空标志位,如果TDR寄存器数据是空的标志位就会置1
}

/**
 * @brief  串口发送数组
 * @param  数组首地址,数组长度,串口号
 * @retval 无
 */
void Serial_SendArray(uint8_t *Array,uint16_t Length,USART_TypeDef* USARTx)           //  *Array指向待发送数组的首地址,传递数组需要指针
{
	uint16_t i;
	for(i=0;i<Length;i++)
	{
		Serial_SendByte(USARTx,Array[i]);
	}
}

/**
 * @brief  串口发送字符串
 * @param  字符串首地址
 * @retval 无
 */
void Serial_SendString(USART_TypeDef* USARTx,char *String)                             //  字符串自带结束标志位,所以不需要传递结束标志位
{
	uint8_t i;
	for(i=0;String[i]!=0;i++)
	{
		Serial_SendByte(USARTx,String[i]);
	}
}

/*----------计算X的Y次方-----------*/
uint32_t Pow(uint32_t X,uint32_t Y) 
{
	uint32_t Result=1;
	while(Y--)
	{
		Result=Result*X;
	}
	return Result;
}
/*---------------------------------*/

/**
 * @brief  串口发送无符号数字(将数字每一位拆分转化成十六进制发送)
 * @brief  取数字的第x位=数字/10^x%10  转换成16进制=加上偏移地址0x30或'0'
 * @param  数字,数字的长度
 * @retval 无
 */
void Serial_SendNumber(uint32_t Number,uint8_t Length,USART_TypeDef* USARTx)
{
	uint8_t i;
	for(i=1;i<=Length;i++)
	{
		Serial_SendByte(USARTx,Number/Pow(10,Length-i)%10  +0x30);   
	}
}

/**
 * @brief  对printf函数重定向,将输出到屏幕转成输出到串口
 * @param 
 * @retval 
 */
// 设置printf输出的目标串口
static USART_TypeDef* g_printf_usart = USART1;
void Serial_SetPrintfUSART(USART_TypeDef* USARTx)
{g_printf_usart = USARTx;}  // 修改全局变量

// 重定向fputc，实现printf向全局变量指定的串口输出
int fputc(int ch, FILE *f)//  print函数底层
{
    // 调用之前的Serial_SendByte函数，使用全局变量指定的串口
    Serial_SendByte(g_printf_usart, (uint8_t)ch);
    return ch;
}

/**
 * @brief  通过串口打印可变字符串
 * @param 
 * @retval 
 */
void Serial_Printf(USART_TypeDef* USARTx,char *format,...)    //  ...可变参数
{
	char String[100];
	va_list arg;                        //  定义参数列表变量(va_list类型名,arg变量名)
	va_start(arg,format);               //  从format位置接收参数表,放在arg位置
	vsprintf(String,format,arg);        //  使用vsprintf函数将格式化的内容写入String中
	va_end(arg);                        //  释放参数表
	Serial_SendString(USARTx,String);
}



/*----------------------------------------------------------USART库函数
恢复缺省配置
void USART_DeInit(USART_TypeDef* USARTx);
初始化
void USART_Init(USART_TypeDef* USARTx, USART_InitTypeDef* USART_InitStruct);
结构体初始化
void USART_StructInit(USART_InitTypeDef* USART_InitStruct);
USART使能
void USART_Cmd(USART_TypeDef* USARTx, FunctionalState NewState);
发送数据
void USART_SendData(USART_TypeDef* USARTx, uint16_t Data);
接收数据
uint16_t USART_ReceiveData(USART_TypeDef* USARTx);
中断配置
void USART_ITConfig(USART_TypeDef* USARTx, uint16_t USART_IT, FunctionalState NewState);
开始USART到DMA的触发通道
void USART_DMACmd(USART_TypeDef* USARTx, uint16_t USART_DMAReq, FunctionalState NewState);
--------------------------------------------------------------
获取USART寄存器状态,第二个参数选择各个寄存器状态
FlagStatus USART_GetFlagStatus(USART_TypeDef* USARTx, uint16_t USART_FLAG);
清除状态标志位
void USART_ClearFlag(USART_TypeDef* USARTx, uint16_t USART_FLAG);
获取中断状态
ITStatus USART_GetITStatus(USART_TypeDef* USARTx, uint16_t USART_IT);
清除中断状态标志位
void USART_ClearITPendingBit(USART_TypeDef* USARTx, uint16_t USART_IT);
-----------------------------------------------------------------
配置同步时钟输出
void USART_ClockInit(USART_TypeDef* USARTx, USART_ClockInitTypeDef* USART_ClockInitStruct);
void USART_ClockStructInit(USART_ClockInitTypeDef* USART_ClockInitStruct);
------------------------------------------------------------------*/
