#ifndef _MY_PID_H_
#define _MY_PID_H_
// PID参数结构体
typedef struct
{
    float kp;          // 比例系数
    float ki;          // 积分系数
    float kd;          // 微分系数
    float out_min;     // 输出最小值
    float out_max;     // 输出最大值
    float i_min;       // 积分项最小值
    float i_max;       // 积分项最大值
    float deadzone;    // 死区大小
} PidParams_t;

void Target_practice(void);
void Target_practice_reset(void);      //打靶状态重置函数

void app_pid_init(void);				//PID参数初始化
void app_pid_set_target(int x, int y);	//设置目标值坐标
void app_pid_update_position(int x, int y);		//更新当前坐标
void app_pid_calc(void);				//PID计算控制函数
#endif
