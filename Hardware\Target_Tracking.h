#ifndef _TARGET_TRACKING_H_
#define _TARGET_TRACKING_H_

#include "Head.h"

// 跟踪状态枚举
typedef enum {
    TRACK_IDLE = 0,        // 空闲状态
    TRACK_SEARCHING = 1,   // 搜索目标状态  
    TRACK_FOLLOWING = 2    // 连续跟踪状态
} TrackingState_t;

// 跟踪错误类型
typedef enum {
    TRACK_ERROR_NONE = 0,
    TRACK_ERROR_DATA_INVALID = 1,    // 数据无效
    TRACK_ERROR_TARGET_LOST = 2,     // 目标丢失
    TRACK_ERROR_LASER_FAIL = 3,      // 激光故障
    TRACK_ERROR_MOTOR_FAIL = 4,      // 电机故障
    TRACK_ERROR_COMM_TIMEOUT = 5     // 通信超时
} TrackingError_t;

// 激光管理结构体
typedef struct {
    uint8_t laser_enabled;      // 激光使能标志
    uint32_t laser_check_timer; // 激光检查计时器
    uint8_t laser_error_count;  // 激光错误计数
} LaserManager_t;

// 跟踪状态数据结构体
typedef struct {
    TrackingState_t current_state;  // 当前状态
    uint8_t is_tracking;           // 跟踪标志
    uint8_t target_detected;       // 目标检测标志
    uint32_t state_timer;          // 状态计时器
    uint32_t target_lost_timer;    // 目标丢失计时器
    TrackingError_t last_error;    // 最后错误代码
    
    // 最后已知的有效坐标
    float last_valid_target_x;
    float last_valid_target_y;
    float last_valid_purple_x;
    float last_valid_purple_y;
    
    // 激光管理
    LaserManager_t laser_manager;
    
    // 统计信息
    uint32_t total_tracking_time;  // 总跟踪时间(ms)
    uint32_t target_lost_count;    // 目标丢失次数
    uint32_t successful_tracks;    // 成功跟踪次数
} TrackingStatus_t;

// 配置参数结构体
typedef struct {
    uint32_t target_lost_threshold;    // 目标丢失阈值 (ms)
    uint32_t laser_check_interval;     // 激光检查间隔 (ms)
    uint8_t max_laser_error_count;     // 最大激光错误次数
    uint8_t enable_status_display;     // 是否显示状态信息
    uint8_t tracking_precision;       // 跟踪精度 (像素)
} TrackingConfig_t;

// 常量定义
#define TARGET_LOST_THRESHOLD_MS 500    // 目标丢失阈值500ms
#define LASER_CHECK_INTERVAL_MS 100     // 激光检查间隔100ms
#define MAX_LASER_ERROR_COUNT 3         // 最大激光错误次数
#define TRACKING_PRECISION_PIXELS 3     // 跟踪精度3像素
#define COORD_VALID_MIN 0               // 坐标有效范围最小值
#define COORD_VALID_MAX_X 640           // X坐标有效范围最大值
#define COORD_VALID_MAX_Y 480           // Y坐标有效范围最大值

// 主要函数接口
void Target_Tracking(void);                    // 主跟踪函数 (10ms周期调用)

// 控制接口
void Target_Tracking_Start(void);              // 启动跟踪
void Target_Tracking_Stop(void);               // 停止跟踪  
void Target_Tracking_Reset(void);              // 重置状态
void Target_Tracking_Pause(void);              // 暂停跟踪
void Target_Tracking_Resume(void);             // 恢复跟踪

// 状态查询接口
uint8_t Target_Is_Tracking(void);              // 是否正在跟踪
uint8_t Target_Has_Target(void);               // 是否检测到目标
TrackingState_t Target_Get_State(void);        // 获取当前状态
TrackingError_t Target_Get_Last_Error(void);   // 获取最后错误

// 配置和状态接口
void Target_Set_Config(TrackingConfig_t* config);      // 设置配置
TrackingConfig_t* Target_Get_Config(void);             // 获取配置
TrackingStatus_t* Target_Get_Status(void);             // 获取状态

// 统计信息接口
uint32_t Target_Get_Tracking_Time(void);       // 获取总跟踪时间
uint32_t Target_Get_Lost_Count(void);          // 获取目标丢失次数
void Target_Reset_Statistics(void);            // 重置统计信息

// 内部函数声明
static void Tracking_State_Machine(light_position* light);     // 状态机处理
static uint8_t Validate_Light_Data(light_position* light);     // 数据有效性检查
static void Update_Last_Valid_Data(light_position* light);     // 更新最后有效数据
static void Handle_Tracking_Error(TrackingError_t error);      // 错误处理
static void Laser_Manager_Update(void);                        // 激光管理更新
static void Display_Tracking_Status(void);                     // 显示跟踪状态
static void Execute_PID_Control(light_position* light);        // 执行PID控制

#endif // _TARGET_TRACKING_H_
