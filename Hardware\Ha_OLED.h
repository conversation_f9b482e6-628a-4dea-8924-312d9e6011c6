#ifndef _HA_OLED_H_
#define _HA_OLED_H_

#include <stdint.h>
#include "Ha_OLEDData.H"

#define OLED_8X16				8  /*此参数值不仅用于判断，而且用于计算横向字符偏移，默认值为字体像素宽度*/
#define OLED_6X8				6

#define OLED_UNFILLED			0
#define OLED_FILLED				1

extern uint8_t OLED_DisplayBuf[8][128];    //  显存数组

/*------功能函数------*/
void OLED_Init(void);
void OLED_Update(void);  //  必须使用该函数  将OLED显存数组的内容更新到OLED硬件进行显示
void OLED_Clear(void);
void OLED_ClearArea(int16_t X, int16_t Y, uint8_t Width, uint8_t Height);
void OLED_WriteCommand(uint8_t Command);
void OLED_WriteData(uint8_t *Data, uint8_t Count);
void OLED_SetCursor(uint8_t Page, uint8_t X);

/*------显示字符------*/
void OLED_Printf(int16_t X, int16_t Y, uint8_t FontSize, char *format, ...);
	
void OLED_ShowChar(int16_t X, int16_t Y, char Char, uint8_t Type);
void OLED_ShowString(int16_t X,int16_t Y,char *String,uint8_t Type);
void OLED_ShowImage(int16_t X,int16_t Y,uint8_t Width,uint8_t Height,const uint8_t *Image);
void OLED_ShowChinese(int16_t X,int16_t Y,char *Chinese);

/*------显示数字------*/
void OLED_ShowNum(int16_t X, int16_t Y, uint32_t Number, uint8_t Length, uint8_t FontSize);
void OLED_ShowSignedNum(int16_t X, int16_t Y, int32_t Number, uint8_t Length, uint8_t FontSize);
void OLED_ShowHexNum(int16_t X, int16_t Y, uint32_t Number, uint8_t Length, uint8_t FontSize);
void OLED_ShowBinNum(int16_t X, int16_t Y, uint32_t Number, uint8_t Length, uint8_t FontSize);
void OLED_ShowFloatNum(int16_t X, int16_t Y, double Number, uint8_t IntLength, uint8_t FraLength, uint8_t FontSize);

/*------显示图形------*/
void OLED_DrawPoint(int16_t X, int16_t Y);
uint8_t OLED_GetPoint(int16_t X, int16_t Y);
void OLED_Reverse(void);
void OLED_ReverseArea(int16_t X, int16_t Y, uint8_t Width, uint8_t Height);
void OLED_DrawLine(int16_t X0, int16_t Y0, int16_t X1, int16_t Y1);
void OLED_DrawRectangle(int16_t X, int16_t Y, uint8_t Width, uint8_t Height, uint8_t IsFilled);
void OLED_DrawTriangle(int16_t X0, int16_t Y0, int16_t X1, int16_t Y1, int16_t X2, int16_t Y2, uint8_t IsFilled);
void OLED_DrawCircle(int16_t X, int16_t Y, uint8_t Radius, uint8_t IsFilled);
void OLED_DrawEllipse(int16_t X, int16_t Y, uint8_t A, uint8_t B, uint8_t IsFilled);
void OLED_DrawArc(int16_t X, int16_t Y, uint8_t Radius, int16_t StartAngle, int16_t EndAngle, uint8_t IsFilled);


/*------Ha_OLED_IIC函数声明------*/
void Ha_IIC_Init(void);
void Ha_IIC_Start(void);
void Ha_IIC_Stop(void);
void Ha_SendByte(uint8_t Byte);
uint8_t Ha_ReceiveByte(void);
void Ha_IIC_SendAck(uint8_t AckBit);
uint8_t Ha_IIC_ReceiveAck(void);


/*------画图工具函数声明------*/
uint32_t OLED_Pow(uint32_t X, uint32_t Y);
uint8_t OLED_pnpoly(uint8_t nvert, int16_t *vertx, int16_t *verty, int16_t testx, int16_t testy);
uint8_t OLED_IsInAngle(int16_t X, int16_t Y, int16_t StartAngle, int16_t EndAngle);

#endif

/**
  * 数据存储格式：
  * 纵向8点，高位在下，先从左到右，再从上到下
  * 每一个Bit对应一个像素点
  * 
  *      B0 B0                  B0 B0
  *      B1 B1                  B1 B1
  *      B2 B2                  B2 B2
  *      B3 B3  ------------->  B3 B3 --
  *      B4 B4                  B4 B4  |
  *      B5 B5                  B5 B5  |
  *      B6 B6                  B6 B6  |
  *      B7 B7                  B7 B7  |
  *                                    |
  *  -----------------------------------
  *  |   
  *  |   B0 B0                  B0 B0
  *  |   B1 B1                  B1 B1
  *  |   B2 B2                  B2 B2
  *  --> B3 B3  ------------->  B3 B3
  *      B4 B4                  B4 B4
  *      B5 B5                  B5 B5
  *      B6 B6                  B6 B6
  *      B7 B7                  B7 B7
  * 
  * 坐标轴定义：
  * 左上角为(0, 0)点
  * 横向向右为X轴，取值范围：0~127
  * 纵向向下为Y轴，取值范围：0~63
  * 
  *       0             X轴           127 
  *      .------------------------------->
  *    0 |
  *      |
  *      |
  *      |
  *  Y轴 |
  *      |
  *      |
  *      |
  *   63 |
  *      v
  * 
  */
