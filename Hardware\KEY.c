#include "stm32f10x.h"                  // Device header
#include "Delay.H"
#include "Timer.h"
void KEY_Init(void)
{
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB,ENABLE);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA,ENABLE);
	GPIO_InitTypeDef GPIO_InitStructure;
	//  因为要读取按键的值，所以Mode选择上拉输入即GPIO_Mode_IPU
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_0;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOB,&GPIO_InitStructure);
	
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_4|GPIO_Pin_0|GPIO_Pin_1;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOA,&GPIO_InitStructure);

//	GPIO_InitStructure.GPIO_Mode=GPIO_Mode_IPD;
//	GPIO_InitStructure.GPIO_Pin=GPIO_Pin_12;
//	GPIO_InitStructure.GPIO_Speed=GPIO_Speed_50MHz;
//	GPIO_Init(GPIOA,&GPIO_InitStructure);
}

/*------------------------------版本2按键消抖*/
//每隔1ms触发中断函数进入TIM2_IRQHandler()
//20次后也就是每隔20ms调用Key_Loop()
//Key_Loop();根据两次调用这个函数的值变化来判断按键状态,函数里面调用key_getstate()获取按下的按键值;处理后返回给Key_GetNum(),再返回到main中并且清零 
uint8_t KeyNum;

uint8_t Key_GetNum(void)
{
	uint8_t Temp;
	Temp=KeyNum;
	KeyNum=0;
	return Temp;
}

uint8_t Key_Getstate(void)
{
	uint8_t KeyNum_Flag=0;
//	if(GPIO_ReadInputDataBit(GPIOA,GPIO_Pin_12)==1) KeyNum_Flag=10;		//复位按键
	if(GPIO_ReadInputDataBit(GPIOA,GPIO_Pin_0)==0) KeyNum_Flag=1;		//标定按钮
	if(GPIO_ReadInputDataBit(GPIOA,GPIO_Pin_4)==0) KeyNum_Flag=2;		//打靶按钮
	if(GPIO_ReadInputDataBit(GPIOA,GPIO_Pin_6)==0)  KeyNum_Flag=3;		//打靶Pro按钮
	if(GPIO_ReadInputDataBit(GPIOB,GPIO_Pin_0)==0)  KeyNum_Flag=4;		//持续打靶按钮
	return KeyNum_Flag;
}

void key_Loop(void)
{
	static unsigned char NowState,LastState;    //  两个局部变量,定义按键是否按下的两个状态
	LastState=NowState;                         //  第一次调用都是0,后面就通过key_getstate获取
	NowState=Key_Getstate();                    //  更新状态
	
	if(LastState==1 && NowState==0)             //  按键松手状态:上一个状态按下,下一个没有按下
	{                                           //  如果1和0互换,那么按下后程序就会反应
		KeyNum=1;
	}
	if(LastState==2 && NowState==0)             //  按键松手状态:上一个状态按下,下一个没有按下
	{
		KeyNum=2;
	}
	if(LastState==3 && NowState==0)             //  按键松手状态:上一个状态按下,下一个没有按下
	{
		KeyNum=3;
	}
	if(LastState==4 && NowState==0)             //  按键松手状态:上一个状态按下,下一个没有按下
	{
		KeyNum=4;
	}
}
/*
void TIM2_IRQHandler(void)
{
	if(TIM_GetITStatus(TIM2,TIM_IT_Update)==SET)
	{
		key_Loop();
		TIM_ClearITPendingBit(TIM2,TIM_IT_Update);     //清除中断标志位，否则会一直处于中断状态影响程序运行
	}
//	static uint8_t Timecount_key;
//	Timecount_key++;
//	if(Timecount_key>=20)
//	{
//		Timecount_key=0;
//		key_Loop();
//	}
}
*/


/*---------------------------------*/
