#include "Head.h"                 

//使用说明：使用前请前往Menu.h文件查看菜单结构体
//          按键1(GPIOB_0) :选择下一项菜单
//          按键2(GPIOB_10):进入当前选择的菜单项或进入该菜单项的运行函数
//          按键3:退出程序或返回上一级菜单
//          若创建过多菜单需要前往.md启动文件修改堆内存大小

MenuItem *head;
uint8_t Mode;
/* 在此增加菜单项对应功能的参数的初始化函数(用于初始化对应功能的参数) */

//==============在此添加一级菜单功能函数
//void example()
//{
//	//...
//	while(1)
//	{
//		uint8_t KeyNum=Key_GetNum();
//		if(KeyNum==3)
//		{
//			OLED_Clear();
//			OLED_Update();
//			break;
//		}
//	}
//}



//==============在此添加二级菜单功能函数
void back()
{
	OLED_Clear();
	OLED_ShowString(0,0,"Running..",OLED_8X16);
	OLED_Update();
	Motor_Enable(Usart_below,1,true,false);
	Motor_Enable(Usart_up,1,true,false);
	Delay_ms(5);						//一个电机相邻两个指令之间最少间隔四毫秒
	Return_zero(Usart_below,1,0,false);
	Return_zero(Usart_up,1,0,false);
	while(1)
	{
		uint8_t KeyNum=Key_GetNum();
		if(KeyNum==3)
		{
			Motor_Enable(Usart_below,1,false,false);
			Motor_Enable(Usart_up,1,false,false);
			OLED_Clear();
			OLED_Update();
			break;
		}
	}
}

void around()
{
	OLED_Clear();
	OLED_ShowString(0,0,"Running..",OLED_8X16);
	OLED_Update();
	Motor_Enable(Usart_below,1,true,false);
	Motor_Enable(Usart_up,1,true,false);
	Delay_ms(5);						//一个电机相邻两个指令之间最少间隔四毫秒;
	Return_zero(Usart_below,1,0,false);
	Return_zero(Usart_up,1,0,false);
	
	//左上;
	Delay_ms(1000);
	S_Control(Usart_up,1,0,30,0,100,true,false);
	S_Control(Usart_below,1,0,30,0,110,true,false);
	//右上
	Delay_ms(1000);
	S_Control(Usart_below,1,1,30,0,120,true,false);
	S_Control(Usart_up,1,0,30,0,90,true,false);
	//右下
	Delay_ms(1000);
	S_Control(Usart_up,1,1,30,0,120,true,false);
	//左下
	Delay_ms(1000);
	S_Control(Usart_below,1,0,30,0,120,true,false);
	//左上
	Delay_ms(1000);
	S_Control(Usart_up,1,0,30,0,100,true,false);
	S_Control(Usart_below,1,0,30,0,115,true,false);
	
	while(1)
	{
		uint8_t KeyNum=Key_GetNum();
		if(KeyNum==3)
		{
			Return_zero(Usart_below,1,0,false);
			Return_zero(Usart_up,1,0,false);
			Delay_ms(5);
			Motor_Enable(Usart_below,1,false,false);
			Motor_Enable(Usart_up,1,false,false);
			OLED_Clear();
			OLED_Update();
			break;
		}
	}
}
void round_pro()
{
	app_pid_init();
	// 直接启动激光跟踪功能（不再需要矩形角点数据）
	OLED_Clear();
	OLED_ShowString(0,0,"Laser Track OK!",OLED_8X16);
	OLED_Update();
	Delay_ms(500);  // 显示0.5秒

	// 数据获取成功，启动电机控制
	OLED_Clear();
	OLED_ShowString(0,0,"Starting...",OLED_8X16);
	OLED_Update();

	Motor_Enable(Usart_below,1,true,false);
	Motor_Enable(Usart_up,1,true,false);
	Mode=1;			//置标志位，中断里面传递PID参数

	// 显示运行状态
	OLED_Clear();
	OLED_ShowString(0,0,"Running...",OLED_8X16);
	OLED_ShowString(0,16,"Press Key3 Stop",OLED_8X16);
	OLED_Update();

	while(1)
	{
		uint8_t KeyNum=Key_GetNum();
		if(KeyNum==3)
		{
			Mode=0;  // 先停止控制循环
			Return_zero(Usart_below,1,0,false);
			Return_zero(Usart_up,1,0,false);
			Delay_ms(5);
			Motor_Enable(Usart_below,1,false,false);
			Motor_Enable(Usart_up,1,false,false);
			OLED_Clear();
			OLED_Update();
			break;
		}
	}
}

void round_pro_max()
{
	app_pid_init();
	Motor_Enable(Usart_below,1,true,false);
	Motor_Enable(Usart_up,1,true,false);
	Mode=2;
	OLED_Clear();
	OLED_ShowString(0,0,"Starting...",OLED_8X16);
	OLED_Update();
	while(1)
	{
		uint8_t KeyNum=Key_GetNum();
		if(KeyNum==3)
		{
			Mode=0;  // 先停止控制循环
			Return_zero(Usart_below,1,0,false);
			Return_zero(Usart_up,1,0,false);
			Delay_ms(5);
			Motor_Enable(Usart_below,1,false,false);
			Motor_Enable(Usart_up,1,false,false);
			OLED_Clear();
			OLED_Update();
			break;
		}
	}
}



//一次性/非实时性功能函数放在菜单状态处理函数
//周期性/高实时性功能放在定时器终端服务程序中
/* 中断函数模板(中断函数放到主函数里)
void TIM2_IRQHandler(void)
{
//	if(MODE==0)
//	{
//		Stop();      //  按键N按下返回一级菜单的同时停止程序运行,Stop()函数未完善
//	}

	if(MODE==1)      //  复位功能
	{
        //在此添加具体的功能逻辑,运动控制
		//...   
	}
	if(MODE==2)      //  绕行一周
	{
		//在此添加具体的功能逻辑,运动控制
		//... 
	}
	if(MODE==3)      //  任意绕行
	{
		//在此添加具体的功能逻辑,运动控制
		//... 
	}
}
 ------------------------ */

/* 在初始化函数内部增加菜单项 */
//需要添加功能函数就将NULL改成对应的功能初始化函数即可
void Menu_Init(void)
{
	//创建一级菜单
	head=CreateMenuItem(">>菜单选择",NULL,1,NULL);
	MenuItem *calibra=CreateMenuItem("标定",NULL,1,NULL);
	MenuItem *base=CreateMenuItem("1打靶",NULL,1,NULL);
	MenuItem *improve=CreateMenuItem("2.打靶MAX",NULL,1,NULL);
	//链接一级菜单
	head->next=calibra;
	calibra->next=base;
	base->next=improve;

	
	//创建 基础部分 二级菜单
	base->sub=CreateMenuItem(">>基础部分",NULL,2,head);
	MenuItem *back_=CreateMenuItem("任务1",back,2,head);
	MenuItem *round_=CreateMenuItem("任务2",around,2,head);  //需要添加功能函数就将NULL改成对应的功能初始化函数即可                
	MenuItem *round_pro_=CreateMenuItem("任务3",round_pro,2,head);
	MenuItem *round_pro_max_=CreateMenuItem("任务4",round_pro_max,2,head);
	
	//链接 基础部分 二级菜单
	base->sub->next=back_;
	back_->next=round_;
	round_->next=round_pro_;
	round_pro_->next=round_pro_max_;
	
	
	//创建 检测模式 二级菜单
	//improve->sub=CreateMenuItem(">>发挥部分",NULL,2,head);
	//MenuItem *waiting=CreateMenuItem("waiting",wainting,2,head);   
	
	//链接 检测模式 二级菜单
	//improve->sub->next=startdetect;
	
	//创建与链接多级菜单...
}
/* ------------------------ */


//=================================================================以下为底层代码
/* 菜单创建函数 */
/* char *name：为菜单项命名 */
/* void(*action)()：指定该菜单项的运行函数 */
/* int MenuItemGrade：规定菜单项的级数 */
/* MenuItem *backMenu：该菜单项的上一级（父级）地址 */
/* 返回值：MenuItem*：返回创建菜单项的地址 */
MenuItem* CreateMenuItem(char *name, void(*action)(), uint8_t MenuItemGrade, MenuItem *backMenu)
{
	MenuItem *temp = (MenuItem *)malloc(sizeof(MenuItem));     //malloc函数在堆上分配了一块内存大小为sizeof(MenuItem)字节
	if (temp != NULL)
	{
		temp -> MenuItemGrade = MenuItemGrade;
		temp -> back = backMenu;
		strcpy(temp->name, name);
		temp -> next = NULL;
		temp -> sub = NULL;
		if(action != NULL)temp->action = action;
        else temp->action = NULL;
	}
	return temp;
} 


/* 运行菜单操作系统 */
void MenuRuning(MenuItem* CurrentMenu)
{
	uint8_t ExitFlag=1;    //退出标志位
	uint8_t menuFlag=0;    //菜单选择标志位
	uint8_t First_Init=1;  //首次初始化标志位
	uint8_t Show_Index=0;  //起始项索引
	uint8_t Show_Max=3;    //一页显示最多菜单项(不包括标题)
	uint8_t title_highlight=1;
	
	while(ExitFlag)
	{
		uint8_t Show_falg;					 /*新增显示项目，外部可调用*/
		uint8_t KeyNum=Key_GetNum();      //uint8_t KeyNum=Key_GetNum();	
		MenuItem *tempMenu=CurrentMenu;      //中间暂存指针
		uint8_t i=0;
		
		//屏幕打印显示一级菜单(只需第一次初始化时显示)
		while(tempMenu!=NULL && First_Init)
		{
			OLED_Printf(20,16*i,OLED_8X16,tempMenu->name);
			OLED_Update();
			tempMenu=tempMenu->next;
			i++;
			//将头部标题反色显示
			if(First_Init && tempMenu==NULL){OLED_ReverseArea(0,0,128,16);OLED_Update();}
			if(tempMenu==NULL)First_Init=0;
		}
		
		//第一次按键过后取消标题反色高亮显示
		if(KeyNum!=0 && title_highlight)
		{
			OLED_ClearArea(0,0,128,16);
			OLED_Printf(20,0,OLED_8X16,CurrentMenu->name);
			title_highlight=0;
		}
		
		//将tempMenu重新指向currentMenu头部，参数重置
		tempMenu=CurrentMenu;
		i=0;
		
		//动态计算菜单项数(不包括头部标题)
		uint8_t item_num = 0;
		MenuItem *temp = CurrentMenu->next;
		while(temp!=NULL){item_num++;temp=temp->next;}
		
		//按键选择菜单项(由于做不出来按键上移,所以没有按键上移,下移会循环到第一个菜单项)
		if(KeyNum==1)        //下移
		{
            menuFlag = (menuFlag>=item_num) ? 1 : menuFlag+1; //按键选择菜单项
			//实现翻页效果
			if(menuFlag>Show_Index+Show_Max)
			{
				Show_Index=Show_Index+Show_Max;
			}
			else if(menuFlag < Show_Index) 
			{
				Show_Index = menuFlag-1;
			}
			
			//保留标题,更新菜单项(这步操作可以将之前的反色显示清除)
			OLED_ClearArea(0, 16, 128, 48);
			tempMenu=CurrentMenu->next;
			
			//重定位起始项
			while(tempMenu && i<Show_Index)
			{
				tempMenu=tempMenu->next;
				++i;
			}
			
			//更新屏幕,反色显示
			i=1;
			while(tempMenu && i<=Show_Max)
			{
				OLED_Printf(20,16*i,OLED_8X16,tempMenu->name);
				
				if(menuFlag==Show_Index+i)
				{
					OLED_ReverseArea(0,16*i,128,16);
				}
				i++;
				tempMenu=tempMenu->next;
			}
			OLED_Update();
		}

		//将tempMenu重新指向currentMenu头部，参数重置
		tempMenu=CurrentMenu;
		i=0;
		
		//判断进入哪个子菜单或者运行哪个菜单函数
		if(KeyNum==2)
		{
			OLED_Clear();
			OLED_Update();
			//遍历
			while(tempMenu!=NULL)
			{
				//进入选择的菜单项
				if(i==menuFlag)
				{
					if(tempMenu->sub!=NULL)
					{
						//重置标志位
						menuFlag=0;
						title_highlight=1;
						First_Init=1; 
						Show_Index=0;
						CurrentMenu=tempMenu->sub;  //进入子菜单
						break;
					}
					else
					{
						OLED_Clear();
						OLED_Update();
						tempMenu->action();         //若没有子菜单则运行菜单项对应的函数
						//ExitFlag=0;
						menuFlag=0;
						First_Init=1;
						title_highlight=1;
						Show_Index=0;
						break;
					}
				}
				tempMenu=tempMenu->next;
				i++;
			}
		}
		
		//退出程序或返回上一级菜单
		if(KeyNum==3)
		{
			if(tempMenu->MenuItemGrade!=1)CurrentMenu=CurrentMenu->back;  //不是一级菜单就返回上一级菜单      
			OLED_Clear();
			OLED_Update();
			//重置标志位
			menuFlag=0;
			First_Init=1;
			title_highlight=1;
			Show_Index=0;
		}
		
		if(Show_falg==1)
		{
			//...
		}
	}
	FreeSpace(head);   //释放内存分配
}

/* 释放内存分配 */
void FreeSpace(MenuItem *current)
{
	while(current !=NULL)
	{
		MenuItem* temp=current;
		current=current->next;
		free(temp);
	}
}

//=================================================================以上为底层代码
