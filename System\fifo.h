#ifndef __FIFO_H
#define __FIFO_H

#include "stm32f10x.h"
#include <stdbool.h>

#define 	FIFO_SIZE   128		// 队列长度
typedef struct {
	uint16_t buffer[FIFO_SIZE];  //缓存区
	__IO uint8_t ptrWrite;	//__IO修饰符表示这个变量是一个输入/输出寄存器，通常用于硬件相关操作或在嵌入式开发中，用来保证该变量在访问时能够正确地被读取和写入。
	__IO uint8_t ptrRead;	//count
}FIFO_t;

extern __IO FIFO_t rxFIFO;//接收数据的队列
extern __IO FIFO_t rxFIFO_3;//串口3接收数据的队列

void Init_Queue(void);
void fifo_In(uint16_t data);
uint16_t fifo_Out(void);
bool fifo_isEmpty(void);
uint16_t fifo_queueLength(void);

//串口3专用FIFO函数
void Init_Queue_3(void);
void fifo_In_3(uint16_t data);
uint16_t fifo_Out_3(void);
bool fifo_isEmpty_3(void);
uint16_t fifo_queueLength_3(void);

#endif
