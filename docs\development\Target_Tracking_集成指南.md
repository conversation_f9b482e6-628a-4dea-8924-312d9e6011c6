# 瞄准跟踪模块集成指南

## 1. 文档信息
- **版本**: v1.0
- **创建日期**: 2025-08-02
- **负责人**: <PERSON> (工程师)
- **项目名称**: 瞄准跟踪模块集成指南

## 2. 快速集成步骤

### 2.1 文件添加
将以下文件添加到您的项目中：
```
Hardware/
├── Target_Tracking.h    // 瞄准跟踪头文件
└── Target_Tracking.c    // 瞄准跟踪实现文件
```

### 2.2 头文件包含
在 `Hardware/Head.h` 中添加包含：
```c
#include "Target_Tracking.h"
```

### 2.3 主函数集成
在 `User/main.c` 的定时器中断中添加跟踪模式：

<augment_code_snippet path="User/main.c" mode="EXCERPT">
````c
void TIM2_IRQHandler(void) {
    if(TIM_GetITStatus(TIM2,TIM_IT_Update)==SET) {
        TIM_ClearITPendingBit(TIM2, TIM_IT_Update);
        key_Loop();
        
        switch (Mode) {
            case 1: Target_practice(); break;
            case 2: /* 持续打靶 */ break;
            case 3: /* 轨迹循环 */ break;
            case 4: Target_Tracking(); break; // 新增跟踪模式
            default: break;
        }
    }
}
````
</augment_code_snippet>

### 2.4 按键控制集成
在 `User/main.c` 主循环中添加按键6的处理：

<augment_code_snippet path="User/main.c" mode="EXCERPT">
````c
if(KeyNum == 6) { // 新增按键6用于跟踪模式
    // 启动跟踪模式
    Motor_Enable(Usart_up, 1, true, false);
    Motor_Enable(Usart_below, 1, true, false);
    app_pid_init();
    Target_Tracking_Start();
    Mode = 4; // 跟踪模式
    
    OLED_ShowString(0, 0, "Track Mode", OLED_8X16);
    OLED_ShowString(0, 16, "Press 6 to Exit", OLED_8X16);
    OLED_Update();
    
    flag = 1;
    // 等待退出
    while(flag) {
        uint8_t temp = Key_GetNum();
        if(temp == 6) {
            Target_Tracking_Stop();
            Mode = 0;
            flag = 0;
            OLED_Clear();
            OLED_ShowString(0, 0, "Track Stopped", OLED_8X16);
            OLED_Update();
        }
    }
}
````
</augment_code_snippet>

## 3. 核心功能说明

### 3.1 主要函数接口

#### 3.1.1 控制接口
```c
void Target_Tracking_Start(void);    // 启动跟踪
void Target_Tracking_Stop(void);     // 停止跟踪
void Target_Tracking_Reset(void);    // 重置状态
void Target_Tracking_Pause(void);    // 暂停跟踪
void Target_Tracking_Resume(void);   // 恢复跟踪
```

#### 3.1.2 状态查询接口
```c
uint8_t Target_Is_Tracking(void);              // 是否正在跟踪
uint8_t Target_Has_Target(void);               // 是否检测到目标
TrackingState_t Target_Get_State(void);        // 获取当前状态
TrackingError_t Target_Get_Last_Error(void);   // 获取最后错误
```

#### 3.1.3 统计信息接口
```c
uint32_t Target_Get_Tracking_Time(void);       // 获取总跟踪时间
uint32_t Target_Get_Lost_Count(void);          // 获取目标丢失次数
void Target_Reset_Statistics(void);            // 重置统计信息
```

### 3.2 状态机说明

#### 3.2.1 状态定义
- **TRACK_IDLE**: 空闲状态，等待启动指令
- **TRACK_SEARCHING**: 搜索状态，激光开启，等待目标出现
- **TRACK_FOLLOWING**: 跟踪状态，连续PID控制，激光持续发光

#### 3.2.2 状态转换
```
启动跟踪: TRACK_IDLE → TRACK_SEARCHING
检测到目标: TRACK_SEARCHING → TRACK_FOLLOWING
目标丢失: TRACK_FOLLOWING → TRACK_SEARCHING
停止跟踪: 任何状态 → TRACK_IDLE
```

### 3.3 复用现有框架

#### 3.3.1 数据获取
- 复用 `Get_light_data(&light)` 获取激光和目标坐标
- 使用现有 `light_position` 结构体

#### 3.3.2 PID控制
- 复用 `app_pid_set_target()` 设置目标
- 复用 `app_pid_update_position()` 更新位置
- 复用 `app_pid_calc()` 执行控制

#### 3.3.3 硬件控制
- 复用 `JG_Ton()/JG_Toff()` 控制激光
- 复用 `Stop_Now()` 控制电机
- 复用 `OLED_*` 函数显示状态

## 4. 使用示例

### 4.1 基本使用
```c
// 启动跟踪
Target_Tracking_Start();

// 在10ms定时器中调用
Target_Tracking();

// 检查状态
if(Target_Is_Tracking()) {
    if(Target_Has_Target()) {
        // 正在跟踪目标
    } else {
        // 正在搜索目标
    }
}

// 停止跟踪
Target_Tracking_Stop();
```

### 4.2 高级使用
```c
// 获取详细状态
TrackingStatus_t* status = Target_Get_Status();
printf("跟踪时间: %d秒\n", status->total_tracking_time / 1000);
printf("目标丢失次数: %d\n", status->target_lost_count);

// 自定义配置
TrackingConfig_t config = {
    .target_lost_threshold = 1000,    // 1秒目标丢失阈值
    .laser_check_interval = 50,       // 50ms激光检查间隔
    .max_laser_error_count = 5,       // 最大5次激光错误
    .enable_status_display = 1,       // 启用状态显示
    .tracking_precision = 2           // 2像素跟踪精度
};
Target_Set_Config(&config);
```

### 4.3 错误处理
```c
TrackingError_t error = Target_Get_Last_Error();
switch(error) {
    case TRACK_ERROR_TARGET_LOST:
        printf("目标丢失\n");
        break;
    case TRACK_ERROR_LASER_FAIL:
        printf("激光故障\n");
        break;
    case TRACK_ERROR_MOTOR_FAIL:
        printf("电机故障\n");
        break;
    default:
        break;
}
```

## 5. 配置参数说明

### 5.1 默认配置
```c
target_lost_threshold = 500ms      // 目标丢失阈值
laser_check_interval = 100ms       // 激光检查间隔
max_laser_error_count = 3          // 最大激光错误次数
enable_status_display = 1          // 启用状态显示
tracking_precision = 3             // 跟踪精度(像素)
```

### 5.2 参数调优建议
- **target_lost_threshold**: 根据目标移动速度调整，快速目标可减小到200ms
- **laser_check_interval**: 激光稳定性好可增加到200ms，不稳定减小到50ms
- **tracking_precision**: 高精度要求设为1-2像素，一般应用3-5像素

## 6. 性能特点

### 6.1 实时性
- 10ms周期调用，响应速度快
- PID控制实时更新，跟踪精度高
- 状态机简洁，执行效率高

### 6.2 稳定性
- 完善的异常处理机制
- 目标丢失自动恢复
- 激光状态自动管理

### 6.3 兼容性
- 100%复用现有框架
- 不修改原有代码
- 独立模块设计

## 7. 调试和测试

### 7.1 调试信息
OLED显示内容：
- 第1行：当前状态 (Track_Idle/Searching../Following)
- 第2行：目标状态 (Target_OK/Target_Lost)
- 第3行：跟踪时间 (Time:XXXs)
- 第4行：丢失次数 (Lost:XXX)

### 7.2 测试步骤
1. **基本功能测试**：启动跟踪，观察状态转换
2. **目标跟踪测试**：移动目标，观察激光跟踪效果
3. **异常恢复测试**：遮挡目标，观察重新搜索
4. **长时间稳定性测试**：连续运行30分钟以上

### 7.3 常见问题
- **激光不亮**：检查激光硬件连接和JG_Ton()函数
- **不跟踪目标**：检查Get_light_data()数据有效性
- **跟踪不准确**：调整PID参数或tracking_precision
- **频繁丢失目标**：增大target_lost_threshold值

## 8. 注意事项

### 8.1 硬件要求
- 确保X/Y轴步进电机正常工作
- 确保激光器可以正常开关
- 确保OLED显示屏正常显示
- 确保串口通信稳定

### 8.2 软件要求
- 确保现有PID控制系统正常工作
- 确保Get_light_data()函数返回有效数据
- 确保10ms定时器中断正常工作
- 确保所有依赖的头文件正确包含

### 8.3 使用限制
- 单目标跟踪，不支持多目标
- 依赖现有视觉系统提供目标坐标
- 需要在10ms定时器中断中调用
- 不能与Target_practice()同时使用

## 9. 扩展功能

### 9.1 可扩展特性
- 支持多目标跟踪
- 增加预测算法
- 优化跟踪精度
- 增加轨迹记录

### 9.2 性能优化
- 算法优化减少计算量
- 内存优化减少RAM使用
- 实时性优化提高响应速度

### 9.3 功能增强
- 增加目标识别算法
- 增加自动校准功能
- 增加远程控制接口
- 增加数据记录功能

## 10. 技术支持

如果在集成过程中遇到问题，请检查：
1. 文件是否正确添加到项目
2. 头文件是否正确包含
3. 定时器中断是否正常调用
4. 现有PID系统是否正常工作
5. 硬件连接是否正确

建议在集成前先确保现有的Target_practice()功能正常工作，这样可以确保硬件和基础软件框架没有问题。
