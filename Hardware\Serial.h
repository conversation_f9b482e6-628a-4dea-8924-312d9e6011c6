#ifndef _SERIAL_H_
#define _SERIAL_H_

#include <stdbool.h>
#include "fifo.H"

// 激光坐标数据结构
typedef struct {
    float purple_x;     // 紫色激光X坐标
    float purple_y;     // 紫色激光Y坐标
    float target_x;     // 目标点X坐标
    float target_y;     // 目标点Y坐标
    uint8_t purple_flag;// 紫色激光存在标志 (1存在, 0不存在)
    uint8_t target_flag;// 目标点存在标志 (1存在, 0不存在)
} light_position;

// 数据包类型枚举
typedef enum {
    DATA_TYPE_NONE = 0,
    DATA_TYPE_LASER = 1    // 激光坐标数据包
} DataPacketType_t;

// 双缓冲串口接收相关声明
#define BUFFER_SIZE 36  // 支持最大数据包：矩形角点数据包36字节
typedef struct {
    uint8_t buffer[BUFFER_SIZE];
    uint8_t write_index;
    uint8_t read_index;
    uint8_t data_ready;
    DataPacketType_t packet_type;  // 数据包类型
    uint8_t packet_length;         // 实际数据包长度
} DoubleBuffer_t;

extern DoubleBuffer_t rx_double_buffer;


void Serial_SendByte(uint8_t Byte);
void Serial_Init(void);
void Serial_SendArray(uint8_t *Array,uint16_t Length,USART_TypeDef* USARTx);
void Serial_SendString(USART_TypeDef* USARTx,char *String);
void Serial_SendNumber(uint32_t Number,uint8_t Length,USART_TypeDef* USARTx);
void Serial_Printf(USART_TypeDef* USARTx,char *format,...);
uint8_t Serial_GetRxflag_1(void);
uint8_t Serial_GetRxData(void);

// 双缓冲相关函数
void DoubleBuffer_Init(void);
uint8_t Get_light_data(light_position* laser_data);  //获取完整激光坐标数据
DataPacketType_t Get_packet_type(void);  // 获取当前数据包类型

uint8_t Serial_GetRxflag_2(void);
extern __IO bool rxFrameFlag;	//串口接收标志
extern __IO uint8_t rxCmd[FIFO_SIZE];	//串口数组返回存储
extern __IO uint8_t rxCount;		//返回命令(储存数组)的长度

uint8_t Serial_GetRxflag_3(void);
extern __IO bool rxFrameFlag_3;	//串口3接收标志
extern __IO uint8_t rxCmd_3[FIFO_SIZE];	//串口3数组返回存储
extern __IO uint8_t rxCount_3;		//串口3返回命令(储存数组)的长度
#endif

