# 瞄准模块跟踪任务产品需求文档 (PRD)

## 1. 文档信息
- **版本**: v1.0
- **创建日期**: 2025-08-02
- **负责人**: Emma (产品经理)
- **项目名称**: 瞄准模块连续跟踪系统

## 2. 背景与问题陈述

### 2.1 项目背景
基于现有的STM32自动瞄准系统，需要实现瞄准模块在小车循迹运动过程中的连续跟踪功能。现有的`Target_practice()`函数实现了静态目标的搜索、瞄准和射击，但无法满足动态跟踪的需求。

### 2.2 核心问题
- **现有问题**: `Target_practice()`函数设计为"搜索→瞄准→射击→完成"的一次性流程，不适用于连续跟踪
- **业务需求**: 小车在AB段轨迹循迹运动时，瞄准模块需要持续跟踪目标并保持激光瞄准
- **技术挑战**: 需要在保持现有框架兼容性的前提下，实现连续跟踪逻辑

## 3. 目标与成功指标

### 3.1 项目目标 (Objectives)
1. **连续跟踪**: 瞄准模块能够在小车运动期间持续跟踪目标
2. **激光连续发光**: 运动期间激光笔必须连续发光射向靶面
3. **框架兼容**: 复用现有`Target_practice()`的PID控制和硬件接口
4. **独立模块**: 提供独立的跟踪函数，便于用户集成

### 3.2 关键结果 (Key Results)
- **跟踪精度**: 激光点与目标点误差≤5像素
- **响应速度**: 目标位置变化后，瞄准调整时间≤200ms
- **跟踪稳定性**: 连续跟踪过程中，激光不间断发光
- **兼容性**: 100%复用现有PID控制和电机接口

### 3.3 反向指标 (Counter Metrics)
- 不影响现有`Target_practice()`功能
- 不修改现有硬件接口和PID参数
- 不增加额外的硬件依赖

## 4. 用户画像与用户故事

### 4.1 目标用户
- **主要用户**: STM32嵌入式开发工程师
- **使用场景**: 自动瞄准系统的循迹跟踪应用
- **技术水平**: 熟悉STM32开发和PID控制

### 4.2 用户故事
**作为** 嵌入式开发工程师  
**我希望** 有一个独立的瞄准跟踪函数  
**以便于** 在小车循迹过程中实现连续目标跟踪

**作为** 系统集成者  
**我希望** 新的跟踪功能能够复用现有框架  
**以便于** 快速集成而不需要重构现有代码

## 5. 功能规格详述

### 5.1 核心功能模块

#### 5.1.1 连续跟踪状态机
```
TRACK_IDLE (空闲状态)
    ↓ 启动跟踪
TRACK_SEARCHING (搜索状态)
    ↓ 检测到目标
TRACK_FOLLOWING (跟踪状态) ←→ 持续循环
    ↓ 停止跟踪
TRACK_IDLE
```

#### 5.1.2 状态详细定义
- **TRACK_IDLE**: 空闲状态，等待启动指令
- **TRACK_SEARCHING**: 搜索目标状态，激光开启，等待目标出现
- **TRACK_FOLLOWING**: 连续跟踪状态，PID控制瞄准，激光持续发光

### 5.2 接口设计

#### 5.2.1 主要函数接口
```c
// 主跟踪函数 (10ms周期调用)
void Target_Tracking(void);

// 控制接口
void Target_Tracking_Start(void);    // 启动跟踪
void Target_Tracking_Stop(void);     // 停止跟踪
void Target_Tracking_Reset(void);    // 重置状态

// 状态查询接口
uint8_t Target_Is_Tracking(void);    // 是否正在跟踪
uint8_t Target_Has_Target(void);     // 是否检测到目标
```

#### 5.2.2 复用现有接口
- **数据获取**: `Get_light_data(&light)` - 获取激光和目标坐标
- **PID控制**: `app_pid_set_target()`, `app_pid_update_position()`, `app_pid_calc()`
- **激光控制**: `JG_Ton()`, `JG_Toff()`
- **显示接口**: `OLED_ShowString()`, `OLED_Update()`

### 5.3 业务逻辑规则

#### 5.3.1 跟踪启动条件
- 调用`Target_Tracking_Start()`函数
- 瞄准模块电机已使能
- 系统处于正常工作状态

#### 5.3.2 跟踪执行逻辑
1. **目标搜索**: 激光开启，等待`light.target_flag==1`
2. **连续跟踪**: 检测到目标后，持续执行PID控制
3. **激光管理**: 跟踪期间激光始终保持开启状态
4. **异常处理**: 目标丢失时保持最后位置，等待目标重新出现

#### 5.3.3 跟踪停止条件
- 调用`Target_Tracking_Stop()`函数
- 系统异常或电机失能
- 用户手动停止

### 5.4 边缘情况与异常处理

#### 5.4.1 目标丢失处理
- **短暂丢失** (≤500ms): 保持当前位置，等待目标重新出现
- **长时间丢失** (>500ms): 切换到搜索状态，扩大搜索范围

#### 5.4.2 数据异常处理
- **坐标越界**: 忽略无效数据，使用上一次有效坐标
- **通信中断**: 停止PID控制，保持当前位置

#### 5.4.3 硬件异常处理
- **电机异常**: 立即停止跟踪，关闭激光
- **激光异常**: 记录错误状态，继续跟踪但不发光

## 6. 范围定义

### 6.1 包含功能 (In Scope)
- ✅ 连续目标跟踪算法实现
- ✅ 激光连续发光控制
- ✅ 复用现有PID控制框架
- ✅ 状态机管理和异常处理
- ✅ OLED状态显示
- ✅ 独立函数接口设计

### 6.2 排除功能 (Out of Scope)
- ❌ 小车循迹功能实现
- ❌ 现有`Target_practice()`函数修改
- ❌ 硬件接口和PID参数调整
- ❌ 新增硬件组件
- ❌ 多目标跟踪功能
- ❌ 图像处理算法优化

## 7. 依赖与风险

### 7.1 内部依赖项
- **硬件依赖**: X/Y轴步进电机、激光器、OLED显示屏
- **软件依赖**: 现有PID控制系统、串口通信、定时器中断
- **数据依赖**: `light_position`结构体和`Get_light_data()`函数

### 7.2 外部依赖项
- **视觉系统**: 摄像头和图像处理算法提供目标坐标
- **小车系统**: 小车循迹功能由外部系统实现
- **通信协议**: 串口数据包格式保持稳定

### 7.3 潜在风险
- **性能风险**: 连续PID计算可能影响系统响应速度
- **稳定性风险**: 长时间运行可能导致累积误差
- **兼容性风险**: 新功能可能与现有代码产生冲突

### 7.4 风险缓解策略
- **性能优化**: 优化PID计算频率，避免不必要的计算
- **误差控制**: 定期重置PID积分项，防止累积误差
- **兼容性测试**: 确保新功能不影响现有功能

## 8. 技术实现要点

### 8.1 状态机设计
- 使用枚举定义跟踪状态
- 状态转换逻辑清晰，避免死锁
- 支持状态查询和强制重置

### 8.2 PID控制优化
- 复用现有PID参数和控制逻辑
- 增加目标丢失时的保持机制
- 优化死区判断，提高跟踪精度

### 8.3 激光管理策略
- 跟踪期间激光始终开启
- 异常情况下自动关闭激光
- 提供激光状态查询接口

### 8.4 显示信息设计
- 实时显示跟踪状态
- 显示目标坐标和当前位置
- 显示跟踪误差和系统状态

## 9. 验收标准

### 9.1 功能验收
- [ ] 能够启动和停止连续跟踪
- [ ] 检测到目标后能够持续跟踪
- [ ] 激光在跟踪期间连续发光
- [ ] 目标丢失后能够重新搜索

### 9.2 性能验收
- [ ] 跟踪精度≤5像素误差
- [ ] 响应时间≤200ms
- [ ] 连续运行稳定性≥30分钟
- [ ] 与现有功能100%兼容

### 9.3 接口验收
- [ ] 提供完整的控制接口
- [ ] 状态查询接口正常工作
- [ ] OLED显示信息准确
- [ ] 复用现有硬件接口

## 10. 发布计划

### 10.1 开发阶段
1. **需求分析** (已完成) - 明确功能需求和技术方案
2. **算法设计** (进行中) - 设计连续跟踪算法和状态机
3. **代码实现** (待开始) - 实现核心跟踪函数
4. **集成测试** (待开始) - 与现有系统集成测试
5. **文档完善** (待开始) - 完善使用文档和示例

### 10.2 交付物清单
- [ ] `Target_Tracking.h` - 头文件定义
- [ ] `Target_Tracking.c` - 核心实现代码
- [ ] 使用示例代码
- [ ] 集成指南文档
- [ ] 测试验证报告

### 10.3 里程碑时间点
- **算法设计完成**: 当前任务完成后5分钟
- **代码实现完成**: 算法设计完成后10分钟
- **集成测试完成**: 代码实现完成后5分钟
- **最终交付**: 总计20分钟内完成
