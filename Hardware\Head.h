#ifndef _HEAD_H_
#define _HEAD_H_
#include "stm32f10x.h"
#include <math.H>
#include "Delay.H"
#include "Ha_OLED.H"
#include "Ha_OLEDData.H"
#include "Serial.H"
#include "Key.H"
#include "stepping_motor.H"
#include "fifo.H"
#include "PID.H"
#include "Timer.H"
#include "Menu.H"
#include "My_pid.H"
#include "PID.H"
#include "JG.H"
#include <stdbool.h>
#include <stdint.h>
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#endif

