#include "fifo.h"


__IO FIFO_t rxFIFO = {0};//接收数据的队列
__IO FIFO_t rxFIFO_3 = {0};//串口3接收数据的队列

//初始化队列
void Init_Queue(void)
{
	rxFIFO.ptrRead  =0;
	rxFIFO.ptrWrite =0;
}

//入队列
void fifo_In(uint16_t data)
{
	rxFIFO.buffer[rxFIFO.ptrWrite]=data;
	++rxFIFO.ptrWrite;
	if(rxFIFO.ptrWrite >= FIFO_SIZE)
	{
		rxFIFO.ptrWrite = 0;
	}
}


//出队列
uint16_t fifo_Out(void)
{
	uint16_t element = 0;
	element = rxFIFO.buffer[rxFIFO.ptrRead];
	++rxFIFO.ptrRead;
	if(rxFIFO.ptrRead >= FIFO_SIZE)
	{
		rxFIFO.ptrRead = 0;
	}
	return element;
}

/**
	* @brief   判断空队列
	* @param   无
	* @retval  无
	*/
bool fifo_isEmpty(void)
{
	if(rxFIFO.ptrRead == rxFIFO.ptrWrite)
	{
		return true;
	}

	return false;
}

/**
	* @brief   计算队列长度
	* @param   无
	* @retval  无
	*/
uint16_t fifo_queueLength(void)
{
	if(rxFIFO.ptrRead <= rxFIFO.ptrWrite)
	{
		return (rxFIFO.ptrWrite - rxFIFO.ptrRead);
	}
	else
	{
		return (FIFO_SIZE - rxFIFO.ptrRead + rxFIFO.ptrWrite);
	}
}
//串口3专用FIFO函数实现
//初始化串口3队列
void Init_Queue_3(void)
{
	rxFIFO_3.ptrRead  =0;
	rxFIFO_3.ptrWrite =0;
}

//串口3入队列
void fifo_In_3(uint16_t data)
{
	rxFIFO_3.buffer[rxFIFO_3.ptrWrite]=data;
	++rxFIFO_3.ptrWrite;
	if(rxFIFO_3.ptrWrite >= FIFO_SIZE)
	{
		rxFIFO_3.ptrWrite = 0;
	}
}

//串口3出队列
uint16_t fifo_Out_3(void)
{
	uint16_t element = 0;
	element = rxFIFO_3.buffer[rxFIFO_3.ptrRead];
	++rxFIFO_3.ptrRead;
	if(rxFIFO_3.ptrRead >= FIFO_SIZE)
	{
		rxFIFO_3.ptrRead = 0;
	}
	return element;
}

/**
	* @brief   判断串口3空队列
	* @param   无
	* @retval  无
	*/
bool fifo_isEmpty_3(void)
{
	if(rxFIFO_3.ptrRead == rxFIFO_3.ptrWrite)
	{
		return true;
	}

	return false;
}

/**
	* @brief   计算串口3队列长度
	* @param   无
	* @retval  无
	*/
uint16_t fifo_queueLength_3(void)
{
	if(rxFIFO_3.ptrRead <= rxFIFO_3.ptrWrite)
	{
		return (rxFIFO_3.ptrWrite - rxFIFO_3.ptrRead);
	}
	else
	{
		return (FIFO_SIZE - rxFIFO_3.ptrRead + rxFIFO_3.ptrWrite);
	}
}
