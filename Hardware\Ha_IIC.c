/*-------------------------------------------------------------------------IIC
SCL/SDA  同步  半双工(两个设备通过一根线通信),一对多设备通信
SCL和SDA要配置成开漏输出模式(开关接地,闭合输出低电平(强下拉),断开浮空),添加上拉电阻(弱上拉到高电平)
---------时序
起始条件:SCL高电平期间,SDA从高电平切换到低电平(主机接收到下降沿后会把SCL置低电平)
终止条件:SCL高电平期间,SDA从低电平切换到高电平(终止条件前应先主机不对从机进行应答)
---------
主机发送字节:SCL低电平,数据位从SDA上发送(高位先行),然后SCL置高电平(弱上拉),从机获取数据位(强下拉),循环八次发送一个字节
主机接收字节:SCL低电平,(主机要释放SDA)从机将数据位通过SDA线发送(高位先行),然后释放SCL(回到高电平)主机读取,循环八次
---------
主机/从机发送字节:拉低SDA发送0,放手SDA回弹到高电平发送1
主机发送应答:主机接收完一个字节后在下一个时钟发送0表示应答,发送1表示无应答
主机接收应答:主机发送完一个字节后在下一个时钟(主机接收前需要释放SDA)接收到0表示从机应答,接收到1表示从机无应答

发送应答原理:主机接收一个字节后若要继续接收数据会把SDA置0,若从机读取SDA为1,就释放SDA(终止条件前应先主机不对从机进行应答)
接收应答原理:主机释放SDA时,从机会把SDA拉下来,在SCL高电平期间主机读取SDA
---------------------------*/
#include "stm32f10x.h"                  // Device header
#include "Delay.H"

#define SCL_GPIO_TYPE                   GPIOB
#define SCL_GPIO_PIN                    GPIO_Pin_8

#define SDA_GPIO_TYPE                   GPIOB
#define SDA_GPIO_PIN                    GPIO_Pin_9


/*-------函数作用:对SCL置高电平1或者低电平0----------*/
void Ha_IIC_W_SCL(uint8_t BitValue)
{
	GPIO_WriteBit(SCL_GPIO_TYPE,SCL_GPIO_PIN,(BitAction)BitValue);          //  BitAction强转类型(强转十六进制),全0就0,否则就是1
	Delay_us(10);                                                           //  恢复延时，确保IIC时序稳定
}


/*-------函数作用:对SDA置高电平1或者低电平0----------*/
void Ha_IIC_W_SDA(uint8_t BitValue)
{
	GPIO_WriteBit(SDA_GPIO_TYPE,SDA_GPIO_PIN,(BitAction)BitValue);
	Delay_us(10);                                        // 恢复延时，确保IIC时序稳定
}


/*-------函数作用:读取SDA的值------------------------*/
uint8_t Ha_IIC_R_SDA(void)
{
	uint8_t BitValue;
	BitValue=GPIO_ReadInputDataBit(SDA_GPIO_TYPE,SDA_GPIO_PIN);
	Delay_us(10);  // 恢复延时，确保IIC时序稳定
	return BitValue;
}


/*-------函数作用:IIC引脚软件初始化------------------*/
void Ha_IIC_Init(void)
{
//  将SCL和SDA初始化为开漏输出模式,并且初始化SCL和SDA置高平
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB,ENABLE);
	
	GPIO_InitTypeDef GPIO_InitStructure;
	GPIO_InitStructure.GPIO_Mode=GPIO_Mode_Out_OD;          
	GPIO_InitStructure.GPIO_Pin=SCL_GPIO_PIN|SDA_GPIO_PIN;  
	GPIO_InitStructure.GPIO_Speed=GPIO_Speed_50MHz;
	GPIO_Init(GPIOB,&GPIO_InitStructure);
	
	GPIO_SetBits(GPIOB,SCL_GPIO_PIN|SDA_GPIO_PIN);        
}


/*-------函数作用:开始时序---------------------------*/
void Ha_IIC_Start(void)
{
//  先确保SDA和SCL都是高电平(空闲),兼容重复起始条件
	Ha_IIC_W_SDA(1);
	Ha_IIC_W_SCL(1);
//  起始条件:SCL高电平期间,SDA从高电平切换到低电平,在把SCL置低电平方便与后面时序衔接
	Ha_IIC_W_SDA(0);
	Ha_IIC_W_SCL(0);
}


/*-------函数作用:停止时序--------------------------*/
void Ha_IIC_Stop(void)
{
//  确保SDA是低电平
	Ha_IIC_W_SDA(0);
//  释放SCL和SDA
	Ha_IIC_W_SCL(1);
	Ha_IIC_W_SDA(1);
}


/*-------函数作用:发送一个字节----------------------*/
void Ha_SendByte(uint8_t Byte)
{
	uint8_t temp;
	for(temp=0;temp<8;temp++)
	{
		Ha_IIC_W_SDA(Byte&(0x80>>temp));
		Ha_IIC_W_SCL(1);
		Ha_IIC_W_SCL(0);
	}
}


/*-------函数作用:读取SDA的值-----------------------*/
uint8_t Ha_ReceiveByte(void)
{
	uint8_t BitValue;
	uint8_t temp;
	Ha_IIC_W_SDA(1);                          //  确保主机释放SDA
	for(temp=0;temp<8;temp++)
	{
		Ha_IIC_W_SCL(1);                      //  SCL高电平读取SDA
		BitValue=Ha_IIC_R_SDA()&(0x80>>temp);
		Ha_IIC_W_SCL(0);                      //  读取完释放SDA
	}
	return BitValue;
}


/*-------函数作用:IIC发送应答---0表示应答---1表示非应答*/
void Ha_IIC_SendAck(uint8_t AckBit)
{
	Ha_IIC_W_SDA(AckBit);
	Ha_IIC_W_SCL(1);
	Ha_IIC_W_SCL(0);
}


/*-------函数作用:IIC接收应答---0表示应答---1表示非应答*/
uint8_t Ha_IIC_ReceiveAck(void)
{
	uint8_t AckBit;
	Ha_IIC_W_SDA(1);                           //  主机释放SDA
	Ha_IIC_W_SCL(1);
	AckBit=Ha_IIC_R_SDA();                     //  SCL高电平时读取SDA值,0表示从机应答
	Ha_IIC_W_SCL(0);
	return AckBit;
}



